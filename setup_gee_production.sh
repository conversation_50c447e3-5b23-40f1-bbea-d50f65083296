#!/bin/bash

# 🛰️ Script de Configuration Google Earth Engine pour Gold Sentinel
# Automatise la configuration complète pour la production

set -e  # Arrêter en cas d'erreur

echo "🛰️ CONFIGURATION GOOGLE EARTH ENGINE - GOLD SENTINEL"
echo "=================================================="
echo ""

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Variables
PROJECT_NAME="gold-sentinel-gee"
SERVICE_ACCOUNT_NAME="gold-sentinel-service"
SERVICE_ACCOUNT_EMAIL="${SERVICE_ACCOUNT_NAME}@${PROJECT_NAME}.iam.gserviceaccount.com"
KEY_FILE="gold-sentinel/secrets/gee-service-account.json"

echo -e "${BLUE}📋 Configuration:${NC}"
echo "  - Projet: $PROJECT_NAME"
echo "  - Service Account: $SERVICE_ACCOUNT_NAME"
echo "  - Email: $SERVICE_ACCOUNT_EMAIL"
echo ""

# Fonction pour vérifier si gcloud est installé
check_gcloud() {
    if ! command -v gcloud &> /dev/null; then
        echo -e "${RED}❌ Google Cloud CLI n'est pas installé${NC}"
        echo -e "${YELLOW}📥 Installation automatique...${NC}"
        
        # Installation pour Ubuntu/Debian
        if command -v apt-get &> /dev/null; then
            echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
            curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -
            sudo apt-get update && sudo apt-get install google-cloud-cli
        else
            echo -e "${RED}❌ Veuillez installer Google Cloud CLI manuellement:${NC}"
            echo "https://cloud.google.com/sdk/docs/install"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ Google Cloud CLI installé${NC}"
    fi
}

# Fonction pour l'authentification
authenticate_gcloud() {
    echo -e "${BLUE}🔐 Authentification Google Cloud...${NC}"
    
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
        echo -e "${YELLOW}📝 Veuillez vous connecter à Google Cloud:${NC}"
        gcloud auth login
    else
        echo -e "${GREEN}✅ Déjà authentifié${NC}"
    fi
}

# Fonction pour créer le projet
create_project() {
    echo -e "${BLUE}🏗️ Création du projet $PROJECT_NAME...${NC}"
    
    if gcloud projects describe $PROJECT_NAME &> /dev/null; then
        echo -e "${GREEN}✅ Projet $PROJECT_NAME existe déjà${NC}"
    else
        gcloud projects create $PROJECT_NAME --name="Gold Sentinel GEE"
        echo -e "${GREEN}✅ Projet $PROJECT_NAME créé${NC}"
    fi
    
    # Définir comme projet par défaut
    gcloud config set project $PROJECT_NAME
    echo -e "${GREEN}✅ Projet $PROJECT_NAME défini comme défaut${NC}"
}

# Fonction pour activer les APIs
enable_apis() {
    echo -e "${BLUE}🔌 Activation des APIs nécessaires...${NC}"
    
    apis=(
        "earthengine.googleapis.com"
        "storage.googleapis.com"
        "iam.googleapis.com"
        "cloudresourcemanager.googleapis.com"
    )
    
    for api in "${apis[@]}"; do
        echo -e "${YELLOW}📡 Activation de $api...${NC}"
        gcloud services enable $api
        echo -e "${GREEN}✅ $api activée${NC}"
    done
}

# Fonction pour créer le compte de service
create_service_account() {
    echo -e "${BLUE}👤 Création du compte de service...${NC}"
    
    if gcloud iam service-accounts describe $SERVICE_ACCOUNT_EMAIL &> /dev/null; then
        echo -e "${GREEN}✅ Compte de service existe déjà${NC}"
    else
        gcloud iam service-accounts create $SERVICE_ACCOUNT_NAME \
            --display-name="Gold Sentinel GEE Service Account" \
            --description="Service account pour Gold Sentinel Google Earth Engine"
        echo -e "${GREEN}✅ Compte de service créé${NC}"
    fi
}

# Fonction pour attribuer les rôles
assign_roles() {
    echo -e "${BLUE}🔑 Attribution des rôles...${NC}"
    
    roles=(
        "roles/earthengine.viewer"
        "roles/earthengine.writer"
        "roles/storage.objectViewer"
        "roles/storage.objectCreator"
    )
    
    for role in "${roles[@]}"; do
        echo -e "${YELLOW}🎭 Attribution du rôle $role...${NC}"
        gcloud projects add-iam-policy-binding $PROJECT_NAME \
            --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
            --role="$role"
        echo -e "${GREEN}✅ Rôle $role attribué${NC}"
    done
}

# Fonction pour créer la clé JSON
create_service_key() {
    echo -e "${BLUE}🔐 Création de la clé de service...${NC}"
    
    # Créer le dossier secrets s'il n'existe pas
    mkdir -p "$(dirname "$KEY_FILE")"
    
    if [ -f "$KEY_FILE" ]; then
        echo -e "${YELLOW}⚠️ Clé existante trouvée. Sauvegarde...${NC}"
        cp "$KEY_FILE" "${KEY_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    gcloud iam service-accounts keys create "$KEY_FILE" \
        --iam-account="$SERVICE_ACCOUNT_EMAIL"
    
    echo -e "${GREEN}✅ Clé de service créée: $KEY_FILE${NC}"
    
    # Sécuriser le fichier
    chmod 600 "$KEY_FILE"
    echo -e "${GREEN}✅ Permissions sécurisées${NC}"
}

# Fonction pour configurer les variables d'environnement
setup_environment() {
    echo -e "${BLUE}🌍 Configuration des variables d'environnement...${NC}"
    
    # Backend Django
    ENV_FILE="gold-sentinel/.env"
    
    cat > "$ENV_FILE" << EOF
# Configuration Google Earth Engine
GEE_PROJECT_ID=$PROJECT_NAME
GEE_SERVICE_ACCOUNT_EMAIL=$SERVICE_ACCOUNT_EMAIL
GEE_SERVICE_ACCOUNT_KEY_PATH=secrets/gee-service-account.json

# Configuration Django
DJANGO_SECRET_KEY=$(python -c 'from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())')
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Base de données
DATABASE_URL=sqlite:///db.sqlite3

# Logging
LOG_LEVEL=INFO
EOF
    
    echo -e "${GREEN}✅ Variables d'environnement configurées: $ENV_FILE${NC}"
    
    # Frontend React
    REACT_ENV_FILE=".env"
    
    cat > "$REACT_ENV_FILE" << EOF
# Configuration Frontend
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_GEE_PROJECT_ID=$PROJECT_NAME
REACT_APP_ENVIRONMENT=development
EOF
    
    echo -e "${GREEN}✅ Variables React configurées: $REACT_ENV_FILE${NC}"
}

# Fonction pour installer les dépendances Python
install_python_deps() {
    echo -e "${BLUE}🐍 Installation des dépendances Python...${NC}"
    
    cd gold-sentinel
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        echo -e "${GREEN}✅ Environnement virtuel créé${NC}"
    fi
    
    source venv/bin/activate
    
    # Ajouter earthengine-api aux requirements
    if ! grep -q "earthengine-api" requirements.txt; then
        echo "earthengine-api==0.1.384" >> requirements.txt
        echo "google-auth==2.23.4" >> requirements.txt
        echo "google-auth-oauthlib==1.1.0" >> requirements.txt
        echo "google-cloud-storage==2.10.0" >> requirements.txt
    fi
    
    pip install -r requirements.txt
    echo -e "${GREEN}✅ Dépendances Python installées${NC}"
    
    cd ..
}

# Fonction pour tester la configuration
test_configuration() {
    echo -e "${BLUE}🧪 Test de la configuration...${NC}"
    
    cd gold-sentinel
    source venv/bin/activate
    
    # Test d'authentification Earth Engine
    python << EOF
import os
import ee
import json

try:
    # Charger les credentials
    with open('secrets/gee-service-account.json', 'r') as f:
        service_account_info = json.load(f)
    
    credentials = ee.ServiceAccountCredentials(
        service_account_info['client_email'],
        'secrets/gee-service-account.json'
    )
    
    ee.Initialize(credentials, project='$PROJECT_NAME')
    
    # Test simple
    image = ee.Image('COPERNICUS/S2_SR_HARMONIZED/20240101T000000_20240101T000000_T31TCJ')
    print("✅ Authentification Google Earth Engine réussie")
    print(f"✅ Projet: $PROJECT_NAME")
    print("✅ Test d'accès aux images Sentinel-2 réussi")
    
except Exception as e:
    print(f"❌ Erreur: {e}")
    exit(1)
EOF
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Configuration Google Earth Engine validée${NC}"
    else
        echo -e "${RED}❌ Erreur dans la configuration${NC}"
        exit 1
    fi
    
    cd ..
}

# Fonction pour créer un script de test
create_test_script() {
    echo -e "${BLUE}📝 Création du script de test...${NC}"
    
    cat > "test_gee_real.py" << 'EOF'
#!/usr/bin/env python3
"""
Test des vraies API Google Earth Engine
"""

import os
import sys
import json
import ee
from datetime import datetime, timedelta

def test_gee_authentication():
    """Test d'authentification GEE"""
    print("🔐 Test d'authentification Google Earth Engine...")
    
    try:
        # Charger les credentials
        key_path = 'gold-sentinel/secrets/gee-service-account.json'
        with open(key_path, 'r') as f:
            service_account_info = json.load(f)
        
        credentials = ee.ServiceAccountCredentials(
            service_account_info['client_email'],
            key_path
        )
        
        project_id = service_account_info['project_id']
        ee.Initialize(credentials, project=project_id)
        
        print(f"✅ Authentification réussie pour le projet: {project_id}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'authentification: {e}")
        return False

def test_sentinel2_access():
    """Test d'accès aux images Sentinel-2"""
    print("📡 Test d'accès aux images Sentinel-2...")
    
    try:
        # Zone de Bondoukou, Côte d'Ivoire
        geometry = ee.Geometry.Rectangle([
            -3.0, 7.8,  # ouest, sud
            -2.6, 8.2   # est, nord
        ])
        
        # Collection Sentinel-2 des 30 derniers jours
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        collection = (ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED')
                     .filterBounds(geometry)
                     .filterDate(start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d'))
                     .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', 20))
                     .sort('system:time_start', False)
                     .limit(5))
        
        # Récupérer les informations
        image_list = collection.getInfo()
        image_count = len(image_list['features'])
        
        print(f"✅ {image_count} images Sentinel-2 trouvées pour Bondoukou")
        
        if image_count > 0:
            first_image = image_list['features'][0]
            image_id = first_image['id']
            properties = first_image['properties']
            
            print(f"📅 Image la plus récente: {image_id}")
            print(f"☁️ Couverture nuageuse: {properties.get('CLOUDY_PIXEL_PERCENTAGE', 'N/A')}%")
            
            # Test de génération de thumbnail
            image = ee.Image(image_id)
            thumbnail_url = image.getThumbURL({
                'region': geometry,
                'dimensions': 512,
                'format': 'png',
                'bands': ['B4', 'B3', 'B2'],
                'min': 0,
                'max': 3000
            })
            
            print(f"🖼️ Thumbnail généré: {thumbnail_url[:100]}...")
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'accès Sentinel-2: {e}")
        return False

def test_export_capabilities():
    """Test des capacités d'export"""
    print("📤 Test des capacités d'export...")
    
    try:
        # Zone test
        geometry = ee.Geometry.Rectangle([-2.8, 8.0, -2.7, 8.1])
        
        # Image test
        image = (ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED')
                .filterBounds(geometry)
                .first())
        
        if image:
            # Créer une tâche d'export (sans l'exécuter)
            task = ee.batch.Export.image.toDrive(
                image=image.select(['B4', 'B3', 'B2']),
                description='test_export_gold_sentinel',
                folder='gold-sentinel-test',
                region=geometry,
                scale=10,
                maxPixels=1e6
            )
            
            print("✅ Capacité d'export validée (tâche créée mais non exécutée)")
            return True
        else:
            print("⚠️ Aucune image disponible pour le test d'export")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test d'export: {e}")
        return False

def main():
    """Test complet"""
    print("🛰️ TEST COMPLET GOOGLE EARTH ENGINE")
    print("=" * 50)
    
    tests = [
        ("Authentification", test_gee_authentication),
        ("Accès Sentinel-2", test_sentinel2_access),
        ("Capacités Export", test_export_capabilities)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}...")
        result = test_func()
        results.append((test_name, result))
        print()
    
    # Résumé
    print("=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"{test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\nScore: {success_count}/{len(tests)} tests réussis")
    
    if success_count == len(tests):
        print("\n🎉 CONFIGURATION GOOGLE EARTH ENGINE COMPLÈTE ET FONCTIONNELLE !")
        print("🚀 Votre système Gold Sentinel est prêt pour la production !")
    else:
        print("\n⚠️ Certains tests ont échoué. Vérifiez la configuration.")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF
    
    chmod +x test_gee_real.py
    echo -e "${GREEN}✅ Script de test créé: test_gee_real.py${NC}"
}

# Fonction principale
main() {
    echo -e "${GREEN}🚀 Début de la configuration automatique...${NC}"
    echo ""
    
    # Vérifications préliminaires
    check_gcloud
    authenticate_gcloud
    
    # Configuration Google Cloud
    create_project
    enable_apis
    create_service_account
    assign_roles
    create_service_key
    
    # Configuration locale
    setup_environment
    install_python_deps
    create_test_script
    
    # Tests
    test_configuration
    
    echo ""
    echo -e "${GREEN}🎉 CONFIGURATION TERMINÉE AVEC SUCCÈS !${NC}"
    echo ""
    echo -e "${BLUE}📋 Prochaines étapes:${NC}"
    echo "1. Testez la configuration: python test_gee_real.py"
    echo "2. Démarrez le backend: cd gold-sentinel && source venv/bin/activate && python manage.py runserver"
    echo "3. Testez l'interface: http://localhost:5173/gee"
    echo ""
    echo -e "${YELLOW}📁 Fichiers créés:${NC}"
    echo "  - $KEY_FILE (clé de service)"
    echo "  - gold-sentinel/.env (variables backend)"
    echo "  - .env (variables frontend)"
    echo "  - test_gee_real.py (script de test)"
    echo ""
    echo -e "${GREEN}✅ Votre système Gold Sentinel est prêt pour la production !${NC}"
}

# Exécution
main "$@"
