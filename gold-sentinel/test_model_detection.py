#!/usr/bin/env python3
"""
Script de test pour le modèle de détection d'orpaillage
Teste le modèle Ghana avec des images satellites réelles
"""

import os
import sys
import django
import numpy as np
import cv2
from pathlib import Path
import requests
from PIL import Image
import matplotlib.pyplot as plt

# Configuration Django
sys.path.append(str(Path(__file__).parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from detection.ai.ghana_detector import GhanaBasedDetector
from image.models.image_model import ImageModel
from region.models.region_model import RegionModel

class ModelTester:
    def __init__(self):
        self.detector = GhanaBasedDetector()
        self.test_images_dir = Path('test_images')
        self.test_images_dir.mkdir(exist_ok=True)
        
    def download_test_images(self):
        """Télécharge des images satellites de test"""
        test_urls = [
            {
                'name': 'bondoukou_mining_1.jpg',
                'url': 'https://via.placeholder.com/512x512/8B4513/FFFFFF?text=Mining+Zone',
                'description': 'Zone minière Bondoukou'
            },
            {
                'name': 'forest_control.jpg', 
                'url': 'https://via.placeholder.com/512x512/228B22/FFFFFF?text=Forest+Control',
                'description': 'Zone forestière témoin'
            }
        ]
        
        print("📥 Téléchargement des images de test...")
        for img_info in test_urls:
            img_path = self.test_images_dir / img_info['name']
            if not img_path.exists():
                try:
                    response = requests.get(img_info['url'], timeout=30)
                    if response.status_code == 200:
                        with open(img_path, 'wb') as f:
                            f.write(response.content)
                        print(f"✅ {img_info['name']} téléchargée")
                    else:
                        print(f"❌ Erreur téléchargement {img_info['name']}: {response.status_code}")
                except Exception as e:
                    print(f"❌ Erreur téléchargement {img_info['name']}: {e}")
                    # Créer une image de test synthétique
                    self.create_synthetic_image(img_path, img_info['description'])
            else:
                print(f"✅ {img_info['name']} déjà présente")
    
    def create_synthetic_image(self, path, description):
        """Crée une image synthétique pour les tests"""
        print(f"🎨 Création d'une image synthétique: {description}")
        
        # Créer une image 512x512 avec des patterns de test
        img = np.zeros((512, 512, 3), dtype=np.uint8)
        
        if 'mining' in description.lower():
            # Simuler une zone minière avec des zones rouges/brunes
            img[:, :] = [139, 69, 19]  # Brun de base
            # Ajouter des zones d'excavation (plus claires)
            cv2.rectangle(img, (100, 100), (200, 200), (205, 133, 63), -1)
            cv2.rectangle(img, (300, 250), (450, 400), (210, 180, 140), -1)
            # Ajouter du bruit réaliste
            noise = np.random.normal(0, 25, img.shape).astype(np.int16)
            img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        else:
            # Simuler une forêt (vert)
            img[:, :] = [34, 139, 34]  # Vert forêt
            # Ajouter de la texture
            noise = np.random.normal(0, 15, img.shape).astype(np.int16)
            img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        # Sauvegarder
        cv2.imwrite(str(path), cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
        print(f"✅ Image synthétique créée: {path}")
    
    def test_model_loading(self):
        """Test le chargement du modèle"""
        print("\n🔍 Test du chargement du modèle...")
        
        try:
            if self.detector.model is not None:
                print("✅ Modèle chargé avec succès")
                print(f"📊 Architecture: {type(self.detector.model)}")
                if hasattr(self.detector.model, 'summary'):
                    print("📋 Résumé du modèle:")
                    self.detector.model.summary()
                return True
            else:
                print("❌ Modèle non chargé - Mode simulation activé")
                return False
        except Exception as e:
            print(f"❌ Erreur lors du test du modèle: {e}")
            return False
    
    def test_image_preprocessing(self):
        """Test le préprocessing des images"""
        print("\n🖼️ Test du préprocessing des images...")
        
        test_image = self.test_images_dir / 'bondoukou_mining_1.jpg'
        if not test_image.exists():
            print("❌ Image de test non trouvée")
            return False
            
        try:
            # Charger et préprocesser l'image
            image_array = self.detector._load_and_preprocess_image(str(test_image))
            
            if image_array is not None:
                print(f"✅ Image préprocessée: {image_array.shape}")
                print(f"📊 Type: {image_array.dtype}, Min: {image_array.min()}, Max: {image_array.max()}")
                
                # Extraire des patches
                patches, coordinates = self.detector._extract_patches(image_array)
                print(f"✅ Patches extraits: {len(patches)} patches de taille {self.detector.patch_size}x{self.detector.patch_size}")
                
                return True
            else:
                print("❌ Erreur lors du préprocessing")
                return False
                
        except Exception as e:
            print(f"❌ Erreur preprocessing: {e}")
            return False
    
    def test_detection_pipeline(self):
        """Test complet du pipeline de détection"""
        print("\n🎯 Test du pipeline de détection...")
        
        # Créer un ImageModel de test
        try:
            # Test avec l'image
            detections = []
            try:
                # Charger l'image directement
                test_image_path = self.test_images_dir / 'bondoukou_mining_1.jpg'
                image_array = self.detector._load_and_preprocess_image(str(test_image_path))
                if image_array is not None:
                    patches, coordinates = self.detector._extract_patches(image_array)
                    
                    # Simuler un ImageModel simple pour les tests
                    class MockImageModel:
                        def __init__(self):
                            self.id = 1
                            
                    mock_image = MockImageModel()
                    detections = self.detector._predict_mining_sites(patches, coordinates, mock_image)
                    
                    print(f"✅ Détections trouvées: {len(detections)}")
                    
                    for i, detection in enumerate(detections[:5]):  # Afficher les 5 premières
                        print(f"  🎯 Détection {i+1}: Type={detection['type']}, Confiance={detection['confidence']:.3f}")
                        
                    return len(detections) > 0
                else:
                    print("❌ Impossible de charger l'image")
                    return False
                    
            except Exception as e:
                print(f"❌ Erreur dans le pipeline: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur setup test: {e}")
            return False
    
    def run_all_tests(self):
        """Lance tous les tests"""
        print("🚀 Démarrage des tests du modèle de détection d'orpaillage")
        print("=" * 60)
        
        # Télécharger les images de test
        self.download_test_images()
        
        # Tests
        tests = [
            ("Chargement du modèle", self.test_model_loading),
            ("Préprocessing des images", self.test_image_preprocessing), 
            ("Pipeline de détection", self.test_detection_pipeline)
        ]
        
        results = {}
        for test_name, test_func in tests:
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"❌ Erreur dans {test_name}: {e}")
                results[test_name] = False
        
        # Résumé
        print("\n" + "=" * 60)
        print("📊 RÉSUMÉ DES TESTS")
        print("=" * 60)
        
        for test_name, success in results.items():
            status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
            print(f"{test_name}: {status}")
        
        total_success = sum(results.values())
        total_tests = len(results)
        print(f"\n🎯 Score global: {total_success}/{total_tests} tests réussis")
        
        if total_success == total_tests:
            print("🎉 Tous les tests sont passés ! Le modèle est prêt.")
        else:
            print("⚠️ Certains tests ont échoué. Vérifiez la configuration.")
        
        return results

if __name__ == "__main__":
    tester = ModelTester()
    tester.run_all_tests()
