"""
Configuration Google Earth Engine pour Gold Sentinel
Gestion des clés API et authentification GEE
"""

import os
import ee
import json
from pathlib import Path
from django.conf import settings

class GoogleEarthEngineConfig:
    def __init__(self):
        self.service_account_file = Path(settings.BASE_DIR) / 'secrets' / 'gee-service-account.json'
        self.project_id = os.getenv('GEE_PROJECT_ID', 'gold-sentinel-gee')
        self.is_authenticated = False
        
    def authenticate(self):
        """Authentification avec Google Earth Engine"""
        try:
            if self.service_account_file.exists():
                # Authentification avec compte de service
                print("🔐 Authentification GEE avec compte de service...")
                
                with open(self.service_account_file, 'r') as f:
                    service_account_info = json.load(f)
                
                credentials = ee.ServiceAccountCredentials(
                    service_account_info['client_email'],
                    str(self.service_account_file)
                )
                
                ee.Initialize(credentials, project=self.project_id)
                self.is_authenticated = True
                print("✅ Authentification GEE réussie")
                
            else:
                # Authentification interactive (développement)
                print("🔐 Authentification GEE interactive...")
                ee.Authenticate()
                ee.Initialize(project=self.project_id)
                self.is_authenticated = True
                print("✅ Authentification GEE interactive réussie")
                
        except Exception as e:
            print(f"❌ Erreur authentification GEE: {e}")
            self.is_authenticated = False
            
    def get_sentinel2_collection(self, region_bounds, start_date, end_date, cloud_coverage=20):
        """Récupère la collection Sentinel-2"""
        if not self.is_authenticated:
            raise Exception("GEE non authentifié")
            
        try:
            # Définir la géométrie de la région
            geometry = ee.Geometry.Rectangle([
                region_bounds['west'], region_bounds['south'],
                region_bounds['east'], region_bounds['north']
            ])
            
            # Collection Sentinel-2
            collection = (ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED')
                         .filterBounds(geometry)
                         .filterDate(start_date, end_date)
                         .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', cloud_coverage))
                         .sort('system:time_start', False))
            
            return collection
            
        except Exception as e:
            print(f"❌ Erreur récupération Sentinel-2: {e}")
            raise
    
    def get_image_info(self, image):
        """Récupère les informations d'une image"""
        try:
            info = image.getInfo()
            properties = info['properties']
            
            return {
                'id': info['id'],
                'date': properties.get('system:time_start'),
                'cloud_coverage': properties.get('CLOUDY_PIXEL_PERCENTAGE', 0),
                'bounds': info.get('geometry', {}).get('coordinates', []),
                'bands': list(info.get('bands', {}).keys()) if 'bands' in info else [],
                'properties': properties
            }
        except Exception as e:
            print(f"❌ Erreur info image: {e}")
            return None
    
    def download_image_thumbnail(self, image, region_bounds, scale=30):
        """Télécharge un thumbnail de l'image"""
        try:
            geometry = ee.Geometry.Rectangle([
                region_bounds['west'], region_bounds['south'],
                region_bounds['east'], region_bounds['north']
            ])
            
            # Paramètres de visualisation
            vis_params = {
                'bands': ['B4', 'B3', 'B2'],  # RGB
                'min': 0,
                'max': 3000,
                'gamma': 1.4
            }
            
            # URL du thumbnail
            url = image.getThumbURL({
                'region': geometry,
                'dimensions': 512,
                'format': 'png',
                **vis_params
            })
            
            return url
            
        except Exception as e:
            print(f"❌ Erreur thumbnail: {e}")
            return None
    
    def export_image_to_drive(self, image, region_bounds, description):
        """Exporte une image vers Google Drive"""
        try:
            geometry = ee.Geometry.Rectangle([
                region_bounds['west'], region_bounds['south'],
                region_bounds['east'], region_bounds['north']
            ])
            
            task = ee.batch.Export.image.toDrive(
                image=image.select(['B2', 'B3', 'B4', 'B8', 'B11', 'B12']),
                description=description,
                folder='gold-sentinel-images',
                region=geometry,
                scale=10,
                maxPixels=1e9
            )
            
            task.start()
            return task.id
            
        except Exception as e:
            print(f"❌ Erreur export: {e}")
            return None

# Configuration des régions ivoiriennes
COTE_IVOIRE_REGIONS = {
    'ZANZAN': {
        'north': 8.5,
        'south': 7.5,
        'east': -2.0,
        'west': -3.5,
        'center': [8.0, -2.75]
    },
    'DENGUELE': {
        'north': 10.5,
        'south': 9.0,
        'east': -6.5,
        'west': -8.0,
        'center': [9.75, -7.25]
    },
    'BOUNKANI': {
        'north': 9.5,
        'south': 8.0,
        'east': -2.5,
        'west': -4.0,
        'center': [8.75, -3.25]
    },
    'BONDOUKOU': {
        'north': 8.2,
        'south': 7.8,
        'east': -2.6,
        'west': -3.0,
        'center': [8.0, -2.8]
    }
}

# Paramètres par défaut
DEFAULT_PARAMS = {
    'cloud_coverage_max': 20,
    'scale': 10,  # mètres par pixel
    'max_images': 50,
    'bands_rgb': ['B4', 'B3', 'B2'],
    'bands_analysis': ['B2', 'B3', 'B4', 'B8', 'B11', 'B12']
}

# Instance globale
gee_config = GoogleEarthEngineConfig()
