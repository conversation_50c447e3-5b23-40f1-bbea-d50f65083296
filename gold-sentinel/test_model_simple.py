#!/usr/bin/env python3
"""
Test simple du modèle de détection d'orpaillage
Sans dépendances Django/GDAL
"""

import os
import numpy as np
import cv2
from pathlib import Path
import requests
from PIL import Image
import matplotlib.pyplot as plt

# Désactiver GPU pour TensorFlow
os.environ['CUDA_VISIBLE_DEVICES'] = ''

try:
    import tensorflow as tf
    tf.config.set_visible_devices([], 'GPU')
    TENSORFLOW_AVAILABLE = True
    print("✅ TensorFlow disponible")
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("❌ TensorFlow non disponible")

class SimpleModelTester:
    def __init__(self):
        self.model_path = Path('ai/models')
        self.model = None
        self.patch_size = 32
        self.confidence_threshold = 0.6
        self.test_images_dir = Path('test_images')
        self.test_images_dir.mkdir(exist_ok=True)
        
    def load_model(self):
        """Charge le modèle Ghana"""
        model_file = self.model_path / 'ghana_model.h5'
        
        if not model_file.exists():
            print(f"❌ Modèle non trouvé: {model_file}")
            return False
            
        if not TENSORFLOW_AVAILABLE:
            print("❌ TensorFlow non disponible")
            return False
            
        try:
            with tf.device('/CPU:0'):
                self.model = tf.keras.models.load_model(str(model_file))
            print(f"✅ Modèle chargé: {model_file}")
            print(f"📊 Input shape: {self.model.input_shape}")
            print(f"📊 Output shape: {self.model.output_shape}")
            return True
        except Exception as e:
            print(f"❌ Erreur chargement modèle: {e}")
            return False
    
    def create_test_images(self):
        """Crée des images de test synthétiques"""
        print("🎨 Création d'images de test...")
        
        # Image 1: Zone minière simulée
        mining_img = np.zeros((512, 512, 3), dtype=np.uint8)
        mining_img[:, :] = [139, 69, 19]  # Brun de base
        
        # Ajouter des zones d'excavation
        cv2.rectangle(mining_img, (100, 100), (200, 200), (205, 133, 63), -1)
        cv2.rectangle(mining_img, (300, 250), (450, 400), (210, 180, 140), -1)
        cv2.circle(mining_img, (150, 350), 50, (160, 82, 45), -1)
        
        # Ajouter du bruit réaliste
        noise = np.random.normal(0, 25, mining_img.shape).astype(np.int16)
        mining_img = np.clip(mining_img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        mining_path = self.test_images_dir / 'mining_zone.jpg'
        cv2.imwrite(str(mining_path), cv2.cvtColor(mining_img, cv2.COLOR_RGB2BGR))
        
        # Image 2: Forêt témoin
        forest_img = np.zeros((512, 512, 3), dtype=np.uint8)
        forest_img[:, :] = [34, 139, 34]  # Vert forêt
        
        # Ajouter de la texture forestière
        for _ in range(100):
            x, y = np.random.randint(0, 512, 2)
            radius = np.random.randint(5, 20)
            color = [np.random.randint(20, 60), np.random.randint(100, 180), np.random.randint(20, 60)]
            cv2.circle(forest_img, (x, y), radius, color, -1)
        
        forest_path = self.test_images_dir / 'forest_control.jpg'
        cv2.imwrite(str(forest_path), cv2.cvtColor(forest_img, cv2.COLOR_RGB2BGR))
        
        print(f"✅ Images créées: {mining_path}, {forest_path}")
        return [mining_path, forest_path]
    
    def preprocess_image(self, image_path):
        """Préprocesse une image pour le modèle"""
        try:
            # Charger l'image
            img = cv2.imread(str(image_path))
            if img is None:
                print(f"❌ Impossible de charger: {image_path}")
                return None
                
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Redimensionner si nécessaire
            if img.shape[:2] != (512, 512):
                img = cv2.resize(img, (512, 512))
            
            # Normaliser
            img = img.astype(np.float32) / 255.0
            
            print(f"✅ Image préprocessée: {img.shape}, range: [{img.min():.3f}, {img.max():.3f}]")
            return img
            
        except Exception as e:
            print(f"❌ Erreur preprocessing: {e}")
            return None
    
    def extract_patches(self, image):
        """Extrait des patches de l'image"""
        patches = []
        coordinates = []
        
        h, w = image.shape[:2]
        step = self.patch_size // 2  # Overlap de 50%
        
        for y in range(0, h - self.patch_size + 1, step):
            for x in range(0, w - self.patch_size + 1, step):
                patch = image[y:y+self.patch_size, x:x+self.patch_size]
                patches.append(patch)
                coordinates.append((x, y))
        
        if patches:
            patches = np.array(patches)
            print(f"✅ Patches extraits: {len(patches)} patches de {self.patch_size}x{self.patch_size}")
            return patches, coordinates
        else:
            return None, None
    
    def predict_mining(self, patches, coordinates):
        """Effectue les prédictions"""
        if self.model is None:
            print("❌ Modèle non chargé")
            return []
            
        try:
            with tf.device('/CPU:0'):
                predictions = self.model.predict(patches, batch_size=32, verbose=0)
            
            detections = []
            for pred, (x, y) in zip(predictions, coordinates):
                confidence = float(pred[0]) if len(pred.shape) == 1 else float(pred.max())
                
                if confidence > self.confidence_threshold:
                    detection_type = self.classify_detection_type(confidence)
                    detections.append({
                        'x': x, 'y': y, 
                        'confidence': confidence, 
                        'type': detection_type
                    })
            
            print(f"✅ Prédictions effectuées: {len(detections)} détections trouvées")
            return detections
            
        except Exception as e:
            print(f"❌ Erreur prédiction: {e}")
            return []
    
    def classify_detection_type(self, confidence):
        """Classifie le type de détection selon la confiance"""
        if confidence > 0.9:
            return 'MINING_SITE'
        elif confidence > 0.8:
            return 'WATER_POLLUTION'
        else:
            return 'ACCESS_ROAD'
    
    def visualize_detections(self, image_path, detections):
        """Visualise les détections sur l'image"""
        try:
            img = cv2.imread(str(image_path))
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Dessiner les détections
            for det in detections:
                x, y = det['x'], det['y']
                conf = det['confidence']
                det_type = det['type']
                
                # Couleur selon le type
                if det_type == 'MINING_SITE':
                    color = (255, 0, 0)  # Rouge
                elif det_type == 'WATER_POLLUTION':
                    color = (255, 165, 0)  # Orange
                else:
                    color = (255, 255, 0)  # Jaune
                
                # Dessiner le rectangle
                cv2.rectangle(img, (x, y), (x + self.patch_size, y + self.patch_size), color, 2)
                
                # Ajouter le texte
                text = f"{det_type[:4]} {conf:.2f}"
                cv2.putText(img, text, (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # Sauvegarder le résultat
            result_path = self.test_images_dir / f"result_{image_path.stem}.jpg"
            cv2.imwrite(str(result_path), cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
            print(f"✅ Résultat sauvé: {result_path}")
            
        except Exception as e:
            print(f"❌ Erreur visualisation: {e}")
    
    def test_model_on_image(self, image_path):
        """Test complet sur une image"""
        print(f"\n🔍 Test sur: {image_path}")
        
        # Préprocesser
        img = self.preprocess_image(image_path)
        if img is None:
            return []
        
        # Extraire patches
        patches, coordinates = self.extract_patches(img)
        if patches is None:
            return []
        
        # Prédire
        detections = self.predict_mining(patches, coordinates)
        
        # Visualiser
        if detections:
            self.visualize_detections(image_path, detections)
            
            print(f"📊 Résumé des détections:")
            for i, det in enumerate(detections[:10]):  # Top 10
                print(f"  {i+1}. {det['type']} - Confiance: {det['confidence']:.3f} - Position: ({det['x']}, {det['y']})")
        else:
            print("❌ Aucune détection trouvée")
        
        return detections
    
    def run_full_test(self):
        """Lance le test complet"""
        print("🚀 Test du modèle de détection d'orpaillage")
        print("=" * 60)
        
        # Charger le modèle
        if not self.load_model():
            print("❌ Impossible de charger le modèle")
            return
        
        # Créer les images de test
        test_images = self.create_test_images()
        
        # Tester sur chaque image
        all_results = {}
        for img_path in test_images:
            detections = self.test_model_on_image(img_path)
            all_results[img_path.name] = detections
        
        # Résumé final
        print("\n" + "=" * 60)
        print("📊 RÉSUMÉ FINAL")
        print("=" * 60)
        
        for img_name, detections in all_results.items():
            print(f"{img_name}: {len(detections)} détections")
            
            # Compter par type
            type_counts = {}
            for det in detections:
                det_type = det['type']
                type_counts[det_type] = type_counts.get(det_type, 0) + 1
            
            for det_type, count in type_counts.items():
                print(f"  - {det_type}: {count}")
        
        print(f"\n🎯 Test terminé ! Résultats dans: {self.test_images_dir}")

if __name__ == "__main__":
    tester = SimpleModelTester()
    tester.run_full_test()
