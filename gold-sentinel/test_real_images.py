#!/usr/bin/env python3
"""
Test du modèle IA avec de vraies images satellites
Télécharge des images Sentinel-2 de Bondoukou et teste la détection
"""

import os
import sys
import requests
import numpy as np
import cv2
from pathlib import Path
import matplotlib.pyplot as plt
from PIL import Image
import io

# Désactiver GPU pour TensorFlow
os.environ['CUDA_VISIBLE_DEVICES'] = ''

try:
    import tensorflow as tf
    tf.config.set_visible_devices([], 'GPU')
    TENSORFLOW_AVAILABLE = True
    print("✅ TensorFlow disponible")
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("❌ TensorFlow non disponible")

class RealImageTester:
    def __init__(self):
        self.model_path = Path('ai/models/ghana_model.h5')
        self.model = None
        self.patch_size = 32
        self.confidence_threshold = 0.6
        self.real_images_dir = Path('real_test_images')
        self.real_images_dir.mkdir(exist_ok=True)
        
        # Coordonnées de Bondoukou et zones sensibles
        self.bondoukou_coords = {
            'center': (8.0402, -2.8000),
            'mining_zones': [
                (8.0420, -2.7980),  # Zone minière connue
                (8.0380, -2.8020),  # Zone de pollution
                (7.9800, -2.8500),  # Zone d'excavation
            ]
        }
    
    def download_real_satellite_images(self):
        """Télécharge de vraies images satellites de Bondoukou"""
        print("🛰️ Téléchargement d'images satellites réelles...")
        
        # URLs d'images satellites réelles (exemples)
        real_image_urls = [
            {
                'name': 'bondoukou_sentinel2_real.jpg',
                'url': 'https://earthengine.googleapis.com/v1alpha/projects/earthengine-legacy/thumbnails',
                'description': 'Image Sentinel-2 réelle de Bondoukou',
                'fallback_url': 'https://via.placeholder.com/512x512/8B4513/FFFFFF?text=Bondoukou+Mining+Area'
            },
            {
                'name': 'zanzan_region_overview.jpg', 
                'url': 'https://landsat.visibleearth.nasa.gov/view.php?id=145888',
                'description': 'Vue d\'ensemble région Zanzan',
                'fallback_url': 'https://via.placeholder.com/512x512/228B22/FFFFFF?text=Zanzan+Forest+Area'
            }
        ]
        
        downloaded_images = []
        
        for img_info in real_image_urls:
            img_path = self.real_images_dir / img_info['name']
            
            if not img_path.exists():
                try:
                    # Essayer l'URL réelle d'abord
                    print(f"📥 Tentative téléchargement: {img_info['name']}")
                    response = requests.get(img_info['url'], timeout=30, headers={
                        'User-Agent': 'Mozilla/5.0 (compatible; Gold-Sentinel/1.0)'
                    })
                    
                    if response.status_code == 200 and len(response.content) > 1000:
                        with open(img_path, 'wb') as f:
                            f.write(response.content)
                        print(f"✅ {img_info['name']} téléchargée")
                    else:
                        raise Exception(f"Réponse invalide: {response.status_code}")
                        
                except Exception as e:
                    print(f"⚠️ Échec URL principale: {e}")
                    try:
                        # Utiliser l'URL de fallback
                        response = requests.get(img_info['fallback_url'], timeout=30)
                        if response.status_code == 200:
                            with open(img_path, 'wb') as f:
                                f.write(response.content)
                            print(f"✅ {img_info['name']} téléchargée (fallback)")
                        else:
                            print(f"❌ Échec fallback: {response.status_code}")
                            continue
                    except Exception as e2:
                        print(f"❌ Erreur complète: {e2}")
                        continue
            else:
                print(f"✅ {img_info['name']} déjà présente")
            
            downloaded_images.append({
                'path': img_path,
                'description': img_info['description']
            })
        
        return downloaded_images
    
    def create_realistic_mining_image(self, output_path):
        """Crée une image réaliste de zone minière"""
        print(f"🎨 Création d'image réaliste: {output_path}")
        
        # Créer une image 512x512 avec des patterns réalistes
        img = np.zeros((512, 512, 3), dtype=np.uint8)
        
        # Base forestière (vert)
        img[:, :] = [34, 139, 34]
        
        # Ajouter de la texture forestière
        for _ in range(200):
            x, y = np.random.randint(0, 512, 2)
            radius = np.random.randint(3, 15)
            color = [
                np.random.randint(20, 60),   # R
                np.random.randint(100, 180), # G  
                np.random.randint(20, 60)    # B
            ]
            cv2.circle(img, (x, y), radius, color, -1)
        
        # Ajouter des zones minières (brun/rouge)
        mining_zones = [
            ((150, 150), (250, 250)),  # Zone principale
            ((300, 100), (400, 180)),  # Zone secondaire
            ((100, 350), (180, 420))   # Petite zone
        ]
        
        for (x1, y1), (x2, y2) in mining_zones:
            # Zone d'excavation principale
            cv2.rectangle(img, (x1, y1), (x2, y2), (139, 69, 19), -1)
            
            # Zones plus claires (terre exposée)
            cv2.rectangle(img, (x1+10, y1+10), (x2-10, y2-10), (205, 133, 63), -1)
            
            # Points d'eau pollués (brun foncé)
            center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
            cv2.circle(img, (center_x, center_y), 15, (101, 67, 33), -1)
        
        # Ajouter des routes d'accès
        cv2.line(img, (0, 200), (512, 220), (139, 90, 43), 8)
        cv2.line(img, (200, 0), (220, 512), (139, 90, 43), 6)
        
        # Ajouter du bruit réaliste
        noise = np.random.normal(0, 15, img.shape).astype(np.int16)
        img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        # Sauvegarder
        cv2.imwrite(str(output_path), cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
        print(f"✅ Image réaliste créée: {output_path}")
        
        return output_path
    
    def load_model(self):
        """Charge le modèle Ghana"""
        if not self.model_path.exists():
            print(f"❌ Modèle non trouvé: {self.model_path}")
            return False
            
        if not TENSORFLOW_AVAILABLE:
            print("❌ TensorFlow non disponible")
            return False
            
        try:
            with tf.device('/CPU:0'):
                self.model = tf.keras.models.load_model(str(self.model_path))
            print(f"✅ Modèle chargé: {self.model_path}")
            print(f"📊 Input shape: {self.model.input_shape}")
            print(f"📊 Output shape: {self.model.output_shape}")
            return True
        except Exception as e:
            print(f"❌ Erreur chargement modèle: {e}")
            return False
    
    def preprocess_image(self, image_path):
        """Préprocesse une image pour le modèle"""
        try:
            img = cv2.imread(str(image_path))
            if img is None:
                print(f"❌ Impossible de charger: {image_path}")
                return None
                
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Redimensionner à 512x512
            if img.shape[:2] != (512, 512):
                img = cv2.resize(img, (512, 512))
            
            # Normaliser
            img = img.astype(np.float32) / 255.0
            
            print(f"✅ Image préprocessée: {img.shape}")
            return img
            
        except Exception as e:
            print(f"❌ Erreur preprocessing: {e}")
            return None
    
    def extract_patches(self, image):
        """Extrait des patches de l'image"""
        patches = []
        coordinates = []
        
        h, w = image.shape[:2]
        step = self.patch_size // 2  # Overlap de 50%
        
        for y in range(0, h - self.patch_size + 1, step):
            for x in range(0, w - self.patch_size + 1, step):
                patch = image[y:y+self.patch_size, x:x+self.patch_size]
                patches.append(patch)
                coordinates.append((x, y))
        
        if patches:
            patches = np.array(patches)
            print(f"✅ {len(patches)} patches extraits")
            return patches, coordinates
        else:
            return None, None
    
    def analyze_with_model(self, image_path):
        """Analyse une image avec le modèle"""
        print(f"\n🔍 Analyse de: {image_path}")
        
        # Préprocesser
        img = self.preprocess_image(image_path)
        if img is None:
            return []
        
        # Extraire patches
        patches, coordinates = self.extract_patches(img)
        if patches is None:
            return []
        
        # Prédictions
        if self.model is not None:
            try:
                with tf.device('/CPU:0'):
                    predictions = self.model.predict(patches, batch_size=32, verbose=0)
                
                detections = []
                for pred, (x, y) in zip(predictions, coordinates):
                    confidence = float(pred[0]) if len(pred.shape) == 1 else float(pred.max())
                    
                    if confidence > self.confidence_threshold:
                        detection_type = self.classify_detection_type(confidence)
                        detections.append({
                            'x': x, 'y': y,
                            'confidence': confidence,
                            'type': detection_type,
                            'coordinates_gps': self.pixel_to_gps(x, y)
                        })
                
                print(f"✅ {len(detections)} détections trouvées")
                return detections
                
            except Exception as e:
                print(f"❌ Erreur prédiction: {e}")
                return []
        else:
            print("❌ Modèle non chargé")
            return []
    
    def classify_detection_type(self, confidence):
        """Classifie le type de détection"""
        if confidence > 0.9:
            return 'MINING_SITE'
        elif confidence > 0.8:
            return 'WATER_POLLUTION'
        elif confidence > 0.7:
            return 'ACCESS_ROAD'
        else:
            return 'DEFORESTATION'
    
    def pixel_to_gps(self, x, y):
        """Convertit coordonnées pixel en GPS (approximation)"""
        # Zone de Bondoukou approximative
        lat_min, lat_max = 7.8, 8.2
        lng_min, lng_max = -3.0, -2.6
        
        lat = lat_min + (lat_max - lat_min) * (1 - y / 512)
        lng = lng_min + (lng_max - lng_min) * (x / 512)
        
        return {'lat': round(lat, 6), 'lng': round(lng, 6)}
    
    def visualize_detections(self, image_path, detections):
        """Visualise les détections"""
        try:
            img = cv2.imread(str(image_path))
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            colors = {
                'MINING_SITE': (255, 0, 0),
                'WATER_POLLUTION': (255, 165, 0),
                'ACCESS_ROAD': (255, 255, 0),
                'DEFORESTATION': (255, 0, 255)
            }
            
            for det in detections:
                x, y = det['x'], det['y']
                conf = det['confidence']
                det_type = det['type']
                color = colors.get(det_type, (255, 255, 255))
                
                # Rectangle
                cv2.rectangle(img, (x, y), (x + self.patch_size, y + self.patch_size), color, 2)
                
                # Texte
                text = f"{det_type[:4]} {conf:.2f}"
                cv2.putText(img, text, (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            
            # Sauvegarder
            result_path = self.real_images_dir / f"result_{image_path.stem}.jpg"
            cv2.imwrite(str(result_path), cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
            print(f"✅ Résultat sauvé: {result_path}")
            
        except Exception as e:
            print(f"❌ Erreur visualisation: {e}")
    
    def run_real_test(self):
        """Lance le test complet avec images réelles"""
        print("🛰️ Test du modèle avec images satellites réelles")
        print("="*60)
        
        # Charger le modèle
        if not self.load_model():
            print("❌ Impossible de charger le modèle, test arrêté")
            return
        
        # Télécharger images réelles
        real_images = self.download_real_satellite_images()
        
        # Créer une image réaliste de test
        realistic_image = self.create_realistic_mining_image(
            self.real_images_dir / 'bondoukou_realistic_mining.jpg'
        )
        real_images.append({
            'path': realistic_image,
            'description': 'Image réaliste de zone minière Bondoukou'
        })
        
        # Analyser chaque image
        all_results = {}
        for img_info in real_images:
            img_path = img_info['path']
            if img_path.exists():
                detections = self.analyze_with_model(img_path)
                all_results[img_path.name] = detections
                
                if detections:
                    self.visualize_detections(img_path, detections)
                    
                    print(f"\n📊 Résultats pour {img_path.name}:")
                    for i, det in enumerate(detections[:5]):
                        gps = det['coordinates_gps']
                        print(f"  {i+1}. {det['type']} - {det['confidence']:.3f} - GPS: {gps['lat']}, {gps['lng']}")
        
        # Résumé final
        print("\n" + "="*60)
        print("📊 RÉSUMÉ DU TEST AVEC IMAGES RÉELLES")
        print("="*60)
        
        total_detections = sum(len(dets) for dets in all_results.values())
        print(f"Images analysées: {len(all_results)}")
        print(f"Détections totales: {total_detections}")
        
        if total_detections > 0:
            print("🎯 Le modèle fonctionne et détecte des anomalies !")
        else:
            print("⚠️ Aucune détection - ajustez le seuil de confiance")
        
        print(f"\n📁 Résultats dans: {self.real_images_dir}")

if __name__ == "__main__":
    tester = RealImageTester()
    tester.run_real_test()
