# 🛰️ Configuration <PERSON>le Google Earth Engine - Gold Sentinel

## 🎯 **ÉTAPES SIMPLES POUR CONFIGURER LES VRAIES API**

### **Étape 1 : Configurer Google Cloud CLI**

```bash
# Ajouter gcloud au PATH
export PATH="$HOME/detectionOpa/google-cloud-sdk/bin:$PATH"

# Vérifier l'installation
gcloud --version

# Authentification
gcloud auth login
```

### **Étape 2 : Créer le Projet Google Cloud**

1. **Aller sur** : https://console.cloud.google.com/
2. **Créer un nouveau projet** :
   - Nom : `gold-sentinel-gee`
   - ID : `gold-sentinel-gee` (ou similaire)
3. **Activer la facturation** (requis pour Earth Engine)

### **Étape 3 : Configurer le Projet avec gcloud**

```bash
# Définir le projet
gcloud config set project gold-sentinel-gee

# Activer les APIs nécessaires
gcloud services enable earthengine.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable iam.googleapis.com
gcloud services enable cloudresourcemanager.googleapis.com
```

### **Étape 4 : Créer le Compte de Service**

```bash
# Créer le compte de service
gcloud iam service-accounts create gold-sentinel-service \
    --display-name="Gold Sentinel GEE Service Account" \
    --description="Service account pour Gold Sentinel Google Earth Engine"

# Attribuer les rôles Earth Engine
gcloud projects add-iam-policy-binding gold-sentinel-gee \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/earthengine.viewer"

gcloud projects add-iam-policy-binding gold-sentinel-gee \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/earthengine.writer"

# Attribuer les rôles Storage
gcloud projects add-iam-policy-binding gold-sentinel-gee \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/storage.objectViewer"

gcloud projects add-iam-policy-binding gold-sentinel-gee \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/storage.objectCreator"
```

### **Étape 5 : Générer la Clé JSON**

```bash
# Créer le dossier secrets
mkdir -p gold-sentinel/secrets

# Générer la clé de service
gcloud iam service-accounts keys create gold-sentinel/secrets/gee-service-account.json \
    --iam-account=<EMAIL>

# Sécuriser le fichier
chmod 600 gold-sentinel/secrets/gee-service-account.json
```

### **Étape 6 : Configurer les Variables d'Environnement**

```bash
# Backend Django (.env dans gold-sentinel/)
cat > gold-sentinel/.env << EOF
# Configuration Google Earth Engine
GEE_PROJECT_ID=gold-sentinel-gee
GEE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GEE_SERVICE_ACCOUNT_KEY_PATH=secrets/gee-service-account.json

# Configuration Django
DJANGO_SECRET_KEY=$(python3 -c 'import secrets; print(secrets.token_urlsafe(50))')
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Base de données
DATABASE_URL=sqlite:///db.sqlite3

# Logging
LOG_LEVEL=INFO
EOF

# Frontend React (.env dans la racine)
cat > .env << EOF
# Configuration Frontend
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_GEE_PROJECT_ID=gold-sentinel-gee
REACT_APP_ENVIRONMENT=development
EOF
```

### **Étape 7 : Installer les Dépendances Python**

```bash
# Aller dans le dossier backend
cd gold-sentinel

# Activer l'environnement virtuel
source venv/bin/activate

# Ajouter les dépendances Earth Engine
echo "earthengine-api==0.1.384" >> requirements.txt
echo "google-auth==2.23.4" >> requirements.txt
echo "google-auth-oauthlib==1.1.0" >> requirements.txt
echo "google-cloud-storage==2.10.0" >> requirements.txt

# Installer les dépendances
pip install -r requirements.txt

# Retourner à la racine
cd ..
```

### **Étape 8 : Tester la Configuration**

```bash
# Test Python simple
cd gold-sentinel
source venv/bin/activate

python << 'EOF'
import os
import ee
import json

try:
    # Charger les credentials
    with open('secrets/gee-service-account.json', 'r') as f:
        service_account_info = json.load(f)
    
    credentials = ee.ServiceAccountCredentials(
        service_account_info['client_email'],
        'secrets/gee-service-account.json'
    )
    
    ee.Initialize(credentials, project='gold-sentinel-gee')
    
    # Test simple
    geometry = ee.Geometry.Rectangle([-3.0, 7.8, -2.6, 8.2])  # Bondoukou
    collection = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED').filterBounds(geometry).limit(1)
    count = collection.size().getInfo()
    
    print("✅ Authentification Google Earth Engine réussie")
    print(f"✅ Test d'accès aux données: {count} image(s) accessible(s)")
    print("✅ Configuration complète et fonctionnelle !")
    
except Exception as e:
    print(f"❌ Erreur: {e}")
EOF

cd ..
```

### **Étape 9 : Démarrer le Système**

```bash
# Démarrer le backend Django
cd gold-sentinel
source venv/bin/activate
python manage.py runserver &

# Dans un autre terminal, démarrer le frontend
cd /home/<USER>/Documents/ADA/FrontProject
npm run dev &
```

### **Étape 10 : Tester l'Interface**

1. **Ouvrir** : http://localhost:5173/gee
2. **Sélectionner région** : ZANZAN
3. **Cliquer "Rechercher Images"**
4. **Vérifier** que de vraies images Sentinel-2 apparaissent
5. **Cliquer "Analyser"** pour tester l'IA

---

## 🚨 **Dépannage Rapide**

### **Erreur d'Authentification**
```bash
gcloud auth login
gcloud auth application-default login
```

### **API Non Activée**
```bash
gcloud services enable earthengine.googleapis.com --project=gold-sentinel-gee
```

### **Problème de Permissions**
```bash
# Vérifier les rôles
gcloud projects get-iam-policy gold-sentinel-gee
```

### **Erreur de Clé**
```bash
# Régénérer la clé
gcloud iam service-accounts keys create gold-sentinel/secrets/gee-service-account.json \
    --iam-account=<EMAIL>
```

---

## ✅ **Checklist de Validation**

- [ ] Google Cloud CLI installé et configuré
- [ ] Projet `gold-sentinel-gee` créé
- [ ] Facturation activée
- [ ] APIs Earth Engine activées
- [ ] Compte de service créé avec les bons rôles
- [ ] Clé JSON générée et sécurisée
- [ ] Variables d'environnement configurées
- [ ] Dépendances Python installées
- [ ] Test d'authentification réussi
- [ ] Interface web accessible
- [ ] Vraies images Sentinel-2 visibles

---

## 🎉 **Félicitations !**

Une fois toutes ces étapes terminées, votre système **Gold Sentinel** sera configuré avec les vraies API Google Earth Engine et prêt pour la surveillance d'orpaillage illégal en Côte d'Ivoire ! 🛰️🇨🇮

**Prochaines étapes :**
1. Former les utilisateurs
2. Configurer les alertes automatiques
3. Planifier la maintenance
4. Déployer en production
