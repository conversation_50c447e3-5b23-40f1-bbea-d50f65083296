# 🛰️ Configuration Google Earth Engine pour Gold Sentinel

## 📋 Guide de Configuration Complète

### **Étape 1 : Créer un Projet Google Cloud**

1. **Aller sur Google Cloud Console** : https://console.cloud.google.com/
2. **Créer un nouveau projet** : `gold-sentinel-gee`
3. **Activer l'API Google Earth Engine** :
   - Aller dans "APIs & Services" > "Library"
   - Rechercher "Earth Engine API"
   - Cliquer "Enable"

### **Étape 2 : Créer un Compte de Service**

1. **Aller dans "IAM & Admin" > "Service Accounts"**
2. **Créer un compte de service** :
   - Nom : `gold-sentinel-gee-service`
   - Description : `Service account pour Gold Sentinel GEE`
3. **Attribuer les rôles** :
   - `Earth Engine Resource Viewer`
   - `Earth Engine Resource Writer`
   - `Storage Object Viewer` (pour les exports)

### **Étape 3 : Générer la Clé JSON**

1. **Cliquer sur le compte de service créé**
2. **Aller dans l'onglet "Keys"**
3. **Cliquer "Add Key" > "Create new key"**
4. **Choisir format JSON**
5. **Télécharger le fichier JSON**

### **Étape 4 : Configuration dans Gold Sentinel**

#### **Backend Django :**

1. **Copier le fichier JSON** :
   ```bash
   cp ~/Downloads/votre-cle-service.json gold-sentinel/secrets/gee-service-account.json
   ```

2. **Mettre à jour les variables d'environnement** :
   ```bash
   # Dans gold-sentinel/.env
   GEE_PROJECT_ID=votre-projet-gee
   GEE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
   ```

3. **Installer les dépendances GEE** :
   ```bash
   cd gold-sentinel
   source venv/bin/activate
   pip install earthengine-api
   ```

#### **Frontend React :**

1. **Créer le fichier .env** :
   ```bash
   # Dans le dossier racine du frontend
   echo "REACT_APP_GEE_API_KEY=votre-api-key" > .env
   echo "REACT_APP_API_URL=http://localhost:8000/api" >> .env
   ```

### **Étape 5 : Test de la Configuration**

#### **Test Backend :**

```bash
cd gold-sentinel
source venv/bin/activate
python manage.py shell

# Dans le shell Python :
from config.gee_config import gee_config
gee_config.authenticate()
# Doit afficher : ✅ Authentification GEE réussie
```

#### **Test Frontend :**

1. **Démarrer le backend** :
   ```bash
   cd gold-sentinel
   source venv/bin/activate
   python manage.py runserver
   ```

2. **Démarrer le frontend** :
   ```bash
   npm start
   ```

3. **Aller sur** : http://localhost:5173/gee-test
4. **Cliquer "Lancer les Tests d'Intégration"**

### **Étape 6 : Configuration Avancée**

#### **Optimisation des Performances :**

1. **Cache Redis** (optionnel) :
   ```bash
   # Installer Redis
   sudo apt install redis-server
   
   # Dans requirements.txt
   redis==4.5.4
   django-redis==5.2.0
   ```

2. **Configuration cache Django** :
   ```python
   # Dans settings.py
   CACHES = {
       'default': {
           'BACKEND': 'django_redis.cache.RedisCache',
           'LOCATION': 'redis://127.0.0.1:6379/1',
           'OPTIONS': {
               'CLIENT_CLASS': 'django_redis.client.DefaultClient',
           }
       }
   }
   ```

#### **Monitoring et Logs :**

1. **Configuration logging** :
   ```python
   # Dans settings.py
   LOGGING = {
       'version': 1,
       'disable_existing_loggers': False,
       'handlers': {
           'gee_file': {
               'level': 'INFO',
               'class': 'logging.FileHandler',
               'filename': 'logs/gee.log',
           },
       },
       'loggers': {
           'config.gee_config': {
               'handlers': ['gee_file'],
               'level': 'INFO',
               'propagate': True,
           },
       },
   }
   ```

### **Étape 7 : Déploiement Production**

#### **Variables d'Environnement Production :**

```bash
# Variables serveur
export GEE_PROJECT_ID=gold-sentinel-prod
export GEE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
export DJANGO_SECRET_KEY=votre-secret-key-production
export DEBUG=False
export ALLOWED_HOSTS=votre-domaine.com,www.votre-domaine.com
```

#### **Sécurité :**

1. **Permissions minimales** pour le compte de service
2. **Rotation régulière** des clés API
3. **Monitoring** des quotas Google Earth Engine
4. **Backup** des configurations

### **🔧 Dépannage**

#### **Erreurs Communes :**

1. **"Authentication failed"** :
   - Vérifier le fichier JSON
   - Vérifier les permissions du compte de service
   - Vérifier que l'API Earth Engine est activée

2. **"Quota exceeded"** :
   - Vérifier les quotas dans Google Cloud Console
   - Optimiser les requêtes (moins d'images, plus de filtres)

3. **"Permission denied"** :
   - Ajouter les rôles Earth Engine au compte de service
   - Vérifier les permissions sur le projet

#### **Commandes de Debug :**

```bash
# Test authentification
python -c "import ee; ee.Authenticate(); ee.Initialize(); print('✅ GEE OK')"

# Test API
curl -X GET "http://localhost:8000/api/gee/statistics/?region=ZANZAN" \
  -H "Authorization: Bearer votre-token"

# Logs Django
tail -f logs/gee.log
```

### **📊 Monitoring**

#### **Métriques à Surveiller :**

- **Nombre de requêtes GEE** par jour
- **Temps de réponse** des analyses
- **Taux d'erreur** des authentifications
- **Usage des quotas** Google Earth Engine

#### **Alertes Recommandées :**

- Quota GEE > 80%
- Temps de réponse > 30 secondes
- Taux d'erreur > 5%
- Échec d'authentification

### **🚀 Optimisations**

#### **Performance :**

1. **Cache des résultats** d'analyse (24h)
2. **Batch processing** des images
3. **Compression** des thumbnails
4. **CDN** pour les images statiques

#### **Coûts :**

1. **Filtrage intelligent** des images
2. **Réutilisation** des analyses existantes
3. **Archivage** des anciennes données
4. **Monitoring** des coûts GEE

---

## ✅ **Checklist de Configuration**

- [ ] Projet Google Cloud créé
- [ ] API Earth Engine activée
- [ ] Compte de service configuré
- [ ] Clé JSON téléchargée et placée
- [ ] Variables d'environnement définies
- [ ] Dépendances installées
- [ ] Tests backend réussis
- [ ] Tests frontend réussis
- [ ] Monitoring configuré
- [ ] Sécurité vérifiée

---

**🎯 Une fois cette configuration terminée, votre système Gold Sentinel sera entièrement opérationnel avec Google Earth Engine !**
