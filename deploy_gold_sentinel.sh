#!/bin/bash

# 🚀 Script de Déploiement Complet - Gold Sentinel
# Automatise tout le processus de A à Z

set -e

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# ASCII Art
show_banner() {
    echo -e "${BLUE}"
    cat << 'EOF'
    ╔═══════════════════════════════════════════════════════════╗
    ║                                                           ║
    ║    🛰️  GOLD SENTINEL - DÉPLOIEMENT PRODUCTION 🛰️         ║
    ║                                                           ║
    ║    Système de Surveillance d'Orpaillage Illégal          ║
    ║    Côte d'Ivoire 🇨🇮 - Google Earth Engine               ║
    ║                                                           ║
    ╚═══════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# Fonction pour afficher une étape
show_step() {
    local step=$1
    local description=$2
    echo ""
    echo -e "${PURPLE}═══════════════════════════════════════════════════════════${NC}"
    echo -e "${YELLOW}📋 ÉTAPE $step: $description${NC}"
    echo -e "${PURPLE}═══════════════════════════════════════════════════════════${NC}"
    echo ""
}

# Fonction pour demander confirmation
confirm() {
    local message=$1
    echo -e "${YELLOW}❓ $message${NC}"
    read -p "Continuer ? (o/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Oo]$ ]]; then
        echo -e "${RED}❌ Opération annulée${NC}"
        exit 1
    fi
}

# Fonction pour vérifier le succès
check_success() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Succès${NC}"
    else
        echo -e "${RED}❌ Échec${NC}"
        exit 1
    fi
}

# Fonction principale
main() {
    show_banner
    
    echo -e "${BLUE}🎯 Ce script va configurer complètement Gold Sentinel avec Google Earth Engine${NC}"
    echo -e "${BLUE}📋 Étapes prévues:${NC}"
    echo "   1. Vérification des prérequis"
    echo "   2. Configuration Google Earth Engine"
    echo "   3. Tests de validation"
    echo "   4. Démarrage du système"
    echo "   5. Vérification finale"
    echo ""
    
    confirm "Êtes-vous prêt à commencer le déploiement complet ?"
    
    # Étape 1: Vérification des prérequis
    show_step "1/5" "Vérification des Prérequis"
    
    echo -e "${BLUE}🔍 Lancement de la vérification...${NC}"
    ./check_prerequisites.sh
    
    if [ $? -ne 0 ]; then
        echo -e "${YELLOW}⚠️ Certains prérequis ne sont pas satisfaits${NC}"
        confirm "Voulez-vous continuer malgré tout ?"
    else
        echo -e "${GREEN}✅ Tous les prérequis sont satisfaits${NC}"
    fi
    
    # Étape 2: Configuration Google Earth Engine
    show_step "2/5" "Configuration Google Earth Engine"
    
    echo -e "${BLUE}🛰️ Configuration des API Google Earth Engine...${NC}"
    echo -e "${YELLOW}📝 Vous allez être redirigé vers Google Cloud pour l'authentification${NC}"
    
    confirm "Avez-vous un compte Google Cloud avec facturation activée ?"
    
    ./setup_gee_production.sh
    check_success
    
    # Étape 3: Tests de validation
    show_step "3/5" "Tests de Validation"
    
    echo -e "${BLUE}🧪 Lancement des tests complets...${NC}"
    python test_gee_real.py
    check_success
    
    # Étape 4: Démarrage du système
    show_step "4/5" "Démarrage du Système"
    
    echo -e "${BLUE}🚀 Démarrage de Gold Sentinel...${NC}"
    ./start_production.sh &
    DEPLOY_PID=$!
    
    # Attendre que les services démarrent
    echo -e "${YELLOW}⏳ Attente du démarrage des services...${NC}"
    sleep 30
    
    # Étape 5: Vérification finale
    show_step "5/5" "Vérification Finale"
    
    echo -e "${BLUE}🔍 Vérification des services...${NC}"
    
    # Test Django
    if curl -s http://localhost:8000/admin/ > /dev/null; then
        echo -e "${GREEN}✅ Backend Django opérationnel${NC}"
    else
        echo -e "${RED}❌ Problème avec le backend Django${NC}"
    fi
    
    # Test React
    if curl -s http://localhost:5173/ > /dev/null; then
        echo -e "${GREEN}✅ Frontend React opérationnel${NC}"
    else
        echo -e "${RED}❌ Problème avec le frontend React${NC}"
    fi
    
    # Test API GEE
    echo -e "${BLUE}🛰️ Test de l'API Google Earth Engine...${NC}"
    cd gold-sentinel
    source venv/bin/activate
    
    python << 'EOF'
import requests
import json

try:
    response = requests.get('http://localhost:8000/api/gee/statistics/?region=ZANZAN', timeout=10)
    if response.status_code == 200:
        data = response.json()
        print("✅ API Google Earth Engine fonctionnelle")
        print(f"📊 Données reçues: {len(str(data))} caractères")
    else:
        print(f"❌ Erreur API: {response.status_code}")
except Exception as e:
    print(f"❌ Erreur connexion API: {e}")
EOF
    
    cd ..
    
    # Affichage final
    echo ""
    echo -e "${PURPLE}═══════════════════════════════════════════════════════════${NC}"
    echo -e "${GREEN}🎉 DÉPLOIEMENT TERMINÉ AVEC SUCCÈS ! 🎉${NC}"
    echo -e "${PURPLE}═══════════════════════════════════════════════════════════${NC}"
    echo ""
    
    echo -e "${BLUE}🛰️ GOLD SENTINEL EST OPÉRATIONNEL !${NC}"
    echo ""
    echo -e "${YELLOW}📱 Interfaces disponibles:${NC}"
    echo "   🛰️  Google Earth Engine:     http://localhost:5173/gee"
    echo "   📊 Tableau de Bord GEE:     http://localhost:5173/gee-dashboard"
    echo "   🧪 Tests d'Intégration:     http://localhost:5173/gee-test"
    echo "   🏠 Dashboard Principal:     http://localhost:5173/dashboard"
    echo ""
    
    echo -e "${YELLOW}🔧 Gestion du système:${NC}"
    echo "   🛑 Arrêter:    ./stop_production.sh"
    echo "   🚀 Redémarrer: ./start_production.sh"
    echo "   📊 Logs:      tail -f logs/django.log"
    echo "   🧪 Tests:     python test_gee_real.py"
    echo ""
    
    echo -e "${YELLOW}📋 Prochaines étapes recommandées:${NC}"
    echo "   1. Tester l'interface web"
    echo "   2. Configurer les alertes automatiques"
    echo "   3. Former les utilisateurs"
    echo "   4. Planifier la maintenance"
    echo ""
    
    echo -e "${GREEN}✅ Votre système de surveillance d'orpaillage est prêt ! 🇨🇮${NC}"
    
    # Ouvrir automatiquement le navigateur
    if command -v xdg-open > /dev/null; then
        echo -e "${BLUE}🌐 Ouverture automatique du navigateur...${NC}"
        sleep 3
        xdg-open http://localhost:5173/gee-dashboard
    elif command -v open > /dev/null; then
        echo -e "${BLUE}🌐 Ouverture automatique du navigateur...${NC}"
        sleep 3
        open http://localhost:5173/gee-dashboard
    fi
}

# Gestion des erreurs
trap 'echo -e "\n${RED}❌ Erreur détectée. Nettoyage...${NC}"; ./stop_production.sh 2>/dev/null; exit 1' ERR

# Gestion de l'interruption
trap 'echo -e "\n${YELLOW}🛑 Déploiement interrompu par l'utilisateur${NC}"; exit 1' INT

# Exécution
main "$@"
