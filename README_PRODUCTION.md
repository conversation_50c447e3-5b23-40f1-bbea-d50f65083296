# 🛰️ Gold Sentinel - Configuration Production Google Earth Engine

## 🚀 Démarrage Rapide

### **Étape 1 : Vérification des Prérequis**

```bash
# Vérifier que tout est prêt
./check_prerequisites.sh
```

### **Étape 2 : Configuration Automatique**

```bash
# Configuration complète en une commande
./setup_gee_production.sh
```

### **Étape 3 : Démarrage du Système**

```bash
# Démarrer Gold Sentinel en production
./start_production.sh
```

### **Étape 4 : Accès aux Interfaces**

- **🛰️ Google Earth Engine** : http://localhost:5173/gee
- **📊 Tableau de Bord GEE** : http://localhost:5173/gee-dashboard  
- **🧪 Tests d'Intégration** : http://localhost:5173/gee-test
- **🏠 Dashboard Principal** : http://localhost:5173/dashboard

---

## 📋 Guide Détaillé

### **🔍 1. Vérification des Prérequis**

Le script `check_prerequisites.sh` vérifie :
- ✅ Outils système (curl, wget, git, python3, pip3)
- ✅ Environnement web (Node.js, npm)
- ✅ Structure du projet
- ✅ Dépendances installées
- ✅ Permissions et connectivité

```bash
./check_prerequisites.sh
```

**Résultat attendu :** 15/16 vérifications réussies minimum

### **🛠️ 2. Configuration Google Earth Engine**

Le script `setup_gee_production.sh` automatise :
- 🔐 Installation Google Cloud CLI
- 🏗️ Création du projet `gold-sentinel-gee`
- 🔌 Activation des APIs nécessaires
- 👤 Création du compte de service
- 🔑 Attribution des rôles appropriés
- 📄 Génération de la clé JSON
- 🌍 Configuration des variables d'environnement
- 🐍 Installation des dépendances Python
- 🧪 Tests de validation

```bash
./setup_gee_production.sh
```

**Durée estimée :** 5-10 minutes

### **🚀 3. Démarrage Production**

Le script `start_production.sh` lance :
- 🐍 Serveur Django (port 8000)
- ⚛️ Serveur React (port 5173)
- 🛰️ Connexion Google Earth Engine
- 📊 Monitoring des services

```bash
./start_production.sh
```

### **🛑 4. Arrêt du Système**

```bash
./stop_production.sh
```

---

## 🔧 Configuration Manuelle (Si Nécessaire)

### **Compte Google Cloud**

1. **Créer un projet** : https://console.cloud.google.com/
2. **Nom du projet** : `gold-sentinel-gee`
3. **Activer la facturation** (requis pour Earth Engine)

### **APIs à Activer**

```bash
gcloud services enable earthengine.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable iam.googleapis.com
gcloud services enable cloudresourcemanager.googleapis.com
```

### **Compte de Service**

```bash
# Créer le compte de service
gcloud iam service-accounts create gold-sentinel-service \
    --display-name="Gold Sentinel GEE Service Account"

# Attribuer les rôles
gcloud projects add-iam-policy-binding gold-sentinel-gee \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/earthengine.viewer"

gcloud projects add-iam-policy-binding gold-sentinel-gee \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/earthengine.writer"

# Générer la clé JSON
gcloud iam service-accounts keys create gold-sentinel/secrets/gee-service-account.json \
    --iam-account=<EMAIL>
```

---

## 🧪 Tests et Validation

### **Test Complet**

```bash
# Test de toute la configuration
python test_gee_real.py
```

### **Test Manuel Python**

```python
import ee
import json

# Charger les credentials
with open('gold-sentinel/secrets/gee-service-account.json', 'r') as f:
    service_account_info = json.load(f)

credentials = ee.ServiceAccountCredentials(
    service_account_info['client_email'],
    'gold-sentinel/secrets/gee-service-account.json'
)

ee.Initialize(credentials, project='gold-sentinel-gee')

# Test d'accès aux données Sentinel-2
geometry = ee.Geometry.Rectangle([-3.0, 7.8, -2.6, 8.2])  # Bondoukou
collection = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED').filterBounds(geometry).limit(1)
print(f"Images trouvées: {collection.size().getInfo()}")
```

### **Test Interface Web**

1. **Aller sur** : http://localhost:5173/gee
2. **Sélectionner région** : ZANZAN
3. **Cliquer "Rechercher Images"**
4. **Cliquer "Analyser"** sur une image
5. **Vérifier les résultats**

---

## 📊 Monitoring et Logs

### **Logs du Système**

```bash
# Logs Django
tail -f logs/django.log

# Logs React
tail -f logs/react.log

# Logs Google Earth Engine
tail -f logs/gee.log
```

### **Statut des Services**

```bash
# Vérifier les ports
lsof -i :8000  # Django
lsof -i :5173  # React

# Vérifier les processus
ps aux | grep "manage.py runserver"
ps aux | grep "vite.*dev"
```

### **Métriques Google Cloud**

```bash
# Utilisation des quotas
gcloud compute project-info describe --project=gold-sentinel-gee

# Logs Earth Engine
gcloud logging read "resource.type=gce_instance AND logName=projects/gold-sentinel-gee/logs/earthengine"
```

---

## 🚨 Dépannage

### **Erreurs Communes**

#### **1. Erreur d'Authentification**

```bash
# Réauthentification
gcloud auth login
gcloud auth application-default login

# Vérifier les permissions
gcloud projects get-iam-policy gold-sentinel-gee
```

#### **2. API Non Activée**

```bash
# Vérifier les APIs
gcloud services list --enabled --project=gold-sentinel-gee

# Réactiver si nécessaire
gcloud services enable earthengine.googleapis.com --project=gold-sentinel-gee
```

#### **3. Quota Dépassé**

```bash
# Vérifier les quotas
gcloud compute project-info describe --project=gold-sentinel-gee
```

#### **4. Problème de Clé de Service**

```bash
# Régénérer la clé
gcloud iam service-accounts keys create gold-sentinel/secrets/gee-service-account.json \
    --iam-account=<EMAIL>
```

### **Commandes de Debug**

```bash
# Test de connectivité
curl -I https://earthengine.googleapis.com

# Vérifier la configuration Django
cd gold-sentinel && source venv/bin/activate && python manage.py check

# Vérifier les dépendances Python
pip list | grep earthengine

# Vérifier les variables d'environnement
cat gold-sentinel/.env
```

---

## 🔒 Sécurité

### **Protection des Clés**

```bash
# Permissions restrictives
chmod 600 gold-sentinel/secrets/gee-service-account.json

# Exclure du contrôle de version
echo "gold-sentinel/secrets/" >> .gitignore
echo "gold-sentinel/.env" >> .gitignore
echo ".env" >> .gitignore
```

### **Rotation des Clés**

```bash
# Créer une nouvelle clé
gcloud iam service-accounts keys create new-key.json \
    --iam-account=<EMAIL>

# Tester la nouvelle clé
# Supprimer l'ancienne clé
gcloud iam service-accounts keys delete KEY_ID \
    --iam-account=<EMAIL>
```

---

## 📈 Optimisations Production

### **Cache Redis**

```bash
# Installer Redis
sudo apt-get install redis-server

# Configuration Django
# Ajouter à settings.py :
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    }
}
```

### **Serveur Web**

```bash
# Nginx + Gunicorn pour Django
sudo apt-get install nginx
pip install gunicorn

# Configuration Nginx
sudo nano /etc/nginx/sites-available/gold-sentinel
```

---

## ✅ Checklist de Déploiement

- [ ] Prérequis vérifiés (`./check_prerequisites.sh`)
- [ ] Configuration GEE terminée (`./setup_gee_production.sh`)
- [ ] Tests réussis (`python test_gee_real.py`)
- [ ] Services démarrés (`./start_production.sh`)
- [ ] Interface web accessible
- [ ] Recherche d'images fonctionnelle
- [ ] Analyse IA opérationnelle
- [ ] Logs configurés
- [ ] Monitoring en place
- [ ] Sécurité vérifiée

---

## 🎉 Félicitations !

Votre système **Gold Sentinel** est maintenant configuré avec les vraies API Google Earth Engine et prêt pour la surveillance d'orpaillage illégal en Côte d'Ivoire ! 🛰️🇨🇮

**Support :** Consultez les logs et utilisez les scripts de dépannage fournis.
