#!/bin/bash

# 🔍 Script de Vérification des Prérequis - Gold Sentinel GEE
# Vérifie que tous les prérequis sont installés avant la configuration

set -e

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "🔍 VÉRIFICATION DES PRÉREQUIS - GOLD SENTINEL"
echo "============================================="
echo ""

# Compteurs
CHECKS_PASSED=0
CHECKS_TOTAL=0

# Fonction pour vérifier une commande
check_command() {
    local cmd=$1
    local name=$2
    local install_hint=$3
    
    CHECKS_TOTAL=$((CHECKS_TOTAL + 1))
    
    if command -v $cmd &> /dev/null; then
        echo -e "${GREEN}✅ $name installé${NC}"
        if [ "$cmd" = "python3" ]; then
            python3 --version
        elif [ "$cmd" = "node" ]; then
            node --version
        elif [ "$cmd" = "npm" ]; then
            npm --version
        fi
        CHECKS_PASSED=$((CHECKS_PASSED + 1))
        return 0
    else
        echo -e "${RED}❌ $name non installé${NC}"
        echo -e "${YELLOW}   💡 Installation: $install_hint${NC}"
        return 1
    fi
}

# Fonction pour vérifier un fichier
check_file() {
    local file=$1
    local name=$2
    
    CHECKS_TOTAL=$((CHECKS_TOTAL + 1))
    
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $name trouvé${NC}"
        CHECKS_PASSED=$((CHECKS_PASSED + 1))
        return 0
    else
        echo -e "${YELLOW}⚠️ $name non trouvé (sera créé)${NC}"
        return 1
    fi
}

# Fonction pour vérifier un dossier
check_directory() {
    local dir=$1
    local name=$2
    
    CHECKS_TOTAL=$((CHECKS_TOTAL + 1))
    
    if [ -d "$dir" ]; then
        echo -e "${GREEN}✅ $name trouvé${NC}"
        CHECKS_PASSED=$((CHECKS_PASSED + 1))
        return 0
    else
        echo -e "${YELLOW}⚠️ $name non trouvé${NC}"
        return 1
    fi
}

echo -e "${BLUE}🔧 Vérification des outils système...${NC}"

# Vérifications des outils système
check_command "curl" "cURL" "sudo apt-get install curl"
check_command "wget" "wget" "sudo apt-get install wget"
check_command "git" "Git" "sudo apt-get install git"
check_command "python3" "Python 3" "sudo apt-get install python3"
check_command "pip3" "pip3" "sudo apt-get install python3-pip"

echo ""
echo -e "${BLUE}🌐 Vérification des outils web...${NC}"

# Vérifications Node.js et npm
check_command "node" "Node.js" "curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt-get install -y nodejs"
check_command "npm" "npm" "Installé avec Node.js"

echo ""
echo -e "${BLUE}📁 Vérification de la structure du projet...${NC}"

# Vérifications de la structure
check_directory "gold-sentinel" "Dossier backend Django"
check_directory "src" "Dossier frontend React"
check_file "package.json" "Configuration npm"
check_file "gold-sentinel/manage.py" "Script Django"

echo ""
echo -e "${BLUE}🐍 Vérification de l'environnement Python...${NC}"

# Vérification de l'environnement virtuel Python
if [ -d "gold-sentinel/venv" ]; then
    echo -e "${GREEN}✅ Environnement virtuel Python trouvé${NC}"
    CHECKS_PASSED=$((CHECKS_PASSED + 1))
else
    echo -e "${YELLOW}⚠️ Environnement virtuel Python non trouvé (sera créé)${NC}"
fi
CHECKS_TOTAL=$((CHECKS_TOTAL + 1))

echo ""
echo -e "${BLUE}📦 Vérification des dépendances Node.js...${NC}"

# Vérification node_modules
if [ -d "node_modules" ]; then
    echo -e "${GREEN}✅ Dépendances Node.js installées${NC}"
    CHECKS_PASSED=$((CHECKS_PASSED + 1))
else
    echo -e "${YELLOW}⚠️ Dépendances Node.js non installées${NC}"
    echo -e "${YELLOW}   💡 Exécutez: npm install${NC}"
fi
CHECKS_TOTAL=$((CHECKS_TOTAL + 1))

echo ""
echo -e "${BLUE}🔐 Vérification des permissions...${NC}"

# Vérification des permissions d'écriture
CHECKS_TOTAL=$((CHECKS_TOTAL + 1))
if [ -w "." ]; then
    echo -e "${GREEN}✅ Permissions d'écriture dans le répertoire courant${NC}"
    CHECKS_PASSED=$((CHECKS_PASSED + 1))
else
    echo -e "${RED}❌ Pas de permissions d'écriture${NC}"
fi

echo ""
echo -e "${BLUE}🌍 Vérification de la connectivité...${NC}"

# Test de connectivité Google
CHECKS_TOTAL=$((CHECKS_TOTAL + 1))
if curl -s --head https://console.cloud.google.com | head -n 1 | grep -q "200 OK"; then
    echo -e "${GREEN}✅ Connectivité Google Cloud OK${NC}"
    CHECKS_PASSED=$((CHECKS_PASSED + 1))
else
    echo -e "${RED}❌ Problème de connectivité Google Cloud${NC}"
fi

# Test de connectivité Earth Engine
CHECKS_TOTAL=$((CHECKS_TOTAL + 1))
if curl -s --head https://earthengine.googleapis.com | head -n 1 | grep -q "200\|404"; then
    echo -e "${GREEN}✅ Connectivité Google Earth Engine OK${NC}"
    CHECKS_PASSED=$((CHECKS_PASSED + 1))
else
    echo -e "${RED}❌ Problème de connectivité Google Earth Engine${NC}"
fi

echo ""
echo "============================================="
echo -e "${BLUE}📊 RÉSUMÉ DE LA VÉRIFICATION${NC}"
echo "============================================="

echo -e "Vérifications réussies: ${GREEN}$CHECKS_PASSED${NC}/$CHECKS_TOTAL"

if [ $CHECKS_PASSED -eq $CHECKS_TOTAL ]; then
    echo -e "${GREEN}🎉 TOUS LES PRÉREQUIS SONT SATISFAITS !${NC}"
    echo -e "${GREEN}✅ Vous pouvez lancer la configuration: ./setup_gee_production.sh${NC}"
    exit 0
elif [ $CHECKS_PASSED -ge $((CHECKS_TOTAL * 3 / 4)) ]; then
    echo -e "${YELLOW}⚠️ La plupart des prérequis sont satisfaits${NC}"
    echo -e "${YELLOW}🔧 Vous pouvez essayer la configuration, mais certains éléments pourraient échouer${NC}"
    echo ""
    echo -e "${BLUE}💡 Actions recommandées:${NC}"
    echo "1. Installez les outils manquants"
    echo "2. Exécutez: npm install"
    echo "3. Relancez cette vérification"
    exit 1
else
    echo -e "${RED}❌ PLUSIEURS PRÉREQUIS MANQUENT${NC}"
    echo -e "${RED}🛑 Veuillez installer les outils manquants avant de continuer${NC}"
    echo ""
    echo -e "${BLUE}💡 Guide d'installation rapide:${NC}"
    echo ""
    echo "# Ubuntu/Debian:"
    echo "sudo apt-get update"
    echo "sudo apt-get install curl wget git python3 python3-pip python3-venv"
    echo ""
    echo "# Node.js (Ubuntu/Debian):"
    echo "curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -"
    echo "sudo apt-get install -y nodejs"
    echo ""
    echo "# Dépendances du projet:"
    echo "npm install"
    echo "cd gold-sentinel && python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
    echo ""
    exit 1
fi
