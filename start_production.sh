#!/bin/bash

# 🚀 Script de Démarrage Production - Gold Sentinel
# Lance le système complet avec les vraies API Google Earth Engine

set -e

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "🚀 DÉMARRAGE PRODUCTION - GOLD SENTINEL"
echo "======================================"
echo ""

# Fonction pour vérifier si un processus tourne sur un port
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Fonction pour arrêter les processus existants
stop_existing_processes() {
    echo -e "${YELLOW}🛑 Arrêt des processus existants...${NC}"
    
    # Arrêter Django (port 8000)
    if check_port 8000; then
        echo "  - Arrêt du serveur Django (port 8000)"
        pkill -f "python.*manage.py.*runserver" || true
        sleep 2
    fi
    
    # Arrêter React (port 5173)
    if check_port 5173; then
        echo "  - Arrêt du serveur React (port 5173)"
        pkill -f "vite.*dev" || true
        pkill -f "npm.*start" || true
        sleep 2
    fi
    
    echo -e "${GREEN}✅ Processus existants arrêtés${NC}"
}

# Fonction pour vérifier la configuration
check_configuration() {
    echo -e "${BLUE}🔍 Vérification de la configuration...${NC}"
    
    # Vérifier les fichiers de configuration
    if [ ! -f "gold-sentinel/.env" ]; then
        echo -e "${RED}❌ Fichier gold-sentinel/.env manquant${NC}"
        echo -e "${YELLOW}💡 Exécutez d'abord: ./setup_gee_production.sh${NC}"
        exit 1
    fi
    
    if [ ! -f "gold-sentinel/secrets/gee-service-account.json" ]; then
        echo -e "${RED}❌ Clé de service Google Earth Engine manquante${NC}"
        echo -e "${YELLOW}💡 Exécutez d'abord: ./setup_gee_production.sh${NC}"
        exit 1
    fi
    
    if [ ! -f ".env" ]; then
        echo -e "${RED}❌ Fichier .env frontend manquant${NC}"
        echo -e "${YELLOW}💡 Exécutez d'abord: ./setup_gee_production.sh${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Configuration validée${NC}"
}

# Fonction pour préparer l'environnement backend
prepare_backend() {
    echo -e "${BLUE}🐍 Préparation du backend Django...${NC}"
    
    cd gold-sentinel
    
    # Activer l'environnement virtuel
    if [ ! -d "venv" ]; then
        echo -e "${YELLOW}📦 Création de l'environnement virtuel...${NC}"
        python3 -m venv venv
    fi
    
    source venv/bin/activate
    
    # Installer/mettre à jour les dépendances
    echo -e "${YELLOW}📦 Installation des dépendances Python...${NC}"
    pip install -q -r requirements.txt
    
    # Migrations Django
    echo -e "${YELLOW}🗄️ Application des migrations...${NC}"
    python manage.py makemigrations --noinput || true
    python manage.py migrate --noinput
    
    # Collecter les fichiers statiques
    echo -e "${YELLOW}📁 Collection des fichiers statiques...${NC}"
    python manage.py collectstatic --noinput || true
    
    cd ..
    echo -e "${GREEN}✅ Backend préparé${NC}"
}

# Fonction pour préparer l'environnement frontend
prepare_frontend() {
    echo -e "${BLUE}⚛️ Préparation du frontend React...${NC}"
    
    # Installer/mettre à jour les dépendances
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        echo -e "${YELLOW}📦 Installation des dépendances Node.js...${NC}"
        npm install
    fi
    
    echo -e "${GREEN}✅ Frontend préparé${NC}"
}

# Fonction pour tester la connexion GEE
test_gee_connection() {
    echo -e "${BLUE}🛰️ Test de la connexion Google Earth Engine...${NC}"
    
    cd gold-sentinel
    source venv/bin/activate
    
    python << 'EOF'
import os
import sys
import json
import ee

try:
    # Charger les credentials
    with open('secrets/gee-service-account.json', 'r') as f:
        service_account_info = json.load(f)
    
    credentials = ee.ServiceAccountCredentials(
        service_account_info['client_email'],
        'secrets/gee-service-account.json'
    )
    
    ee.Initialize(credentials, project=service_account_info['project_id'])
    
    # Test simple
    collection = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED').limit(1)
    count = collection.size().getInfo()
    
    print("✅ Connexion Google Earth Engine réussie")
    print(f"✅ Test d'accès aux données: {count} image(s) accessible(s)")
    
except Exception as e:
    print(f"❌ Erreur connexion GEE: {e}")
    sys.exit(1)
EOF
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Connexion Google Earth Engine validée${NC}"
    else
        echo -e "${RED}❌ Problème de connexion Google Earth Engine${NC}"
        echo -e "${YELLOW}💡 Vérifiez votre configuration avec: python test_gee_real.py${NC}"
        cd ..
        exit 1
    fi
    
    cd ..
}

# Fonction pour démarrer le backend
start_backend() {
    echo -e "${BLUE}🚀 Démarrage du serveur Django...${NC}"
    
    cd gold-sentinel
    source venv/bin/activate
    
    # Démarrer en arrière-plan
    nohup python manage.py runserver 0.0.0.0:8000 > ../logs/django.log 2>&1 &
    DJANGO_PID=$!
    
    # Attendre que le serveur démarre
    echo -e "${YELLOW}⏳ Attente du démarrage du serveur Django...${NC}"
    for i in {1..30}; do
        if curl -s http://localhost:8000/admin/ > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Serveur Django démarré (PID: $DJANGO_PID)${NC}"
            echo $DJANGO_PID > ../logs/django.pid
            break
        fi
        sleep 1
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ Timeout démarrage Django${NC}"
            exit 1
        fi
    done
    
    cd ..
}

# Fonction pour démarrer le frontend
start_frontend() {
    echo -e "${BLUE}🚀 Démarrage du serveur React...${NC}"
    
    # Créer le dossier logs s'il n'existe pas
    mkdir -p logs
    
    # Démarrer en arrière-plan
    nohup npm run dev > logs/react.log 2>&1 &
    REACT_PID=$!
    
    # Attendre que le serveur démarre
    echo -e "${YELLOW}⏳ Attente du démarrage du serveur React...${NC}"
    for i in {1..60}; do
        if curl -s http://localhost:5173/ > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Serveur React démarré (PID: $REACT_PID)${NC}"
            echo $REACT_PID > logs/react.pid
            break
        fi
        sleep 1
        if [ $i -eq 60 ]; then
            echo -e "${RED}❌ Timeout démarrage React${NC}"
            exit 1
        fi
    done
}

# Fonction pour afficher le statut
show_status() {
    echo ""
    echo "======================================"
    echo -e "${GREEN}🎉 GOLD SENTINEL DÉMARRÉ AVEC SUCCÈS !${NC}"
    echo "======================================"
    echo ""
    echo -e "${BLUE}📊 Services actifs:${NC}"
    echo "  🐍 Backend Django:  http://localhost:8000"
    echo "  ⚛️  Frontend React:   http://localhost:5173"
    echo ""
    echo -e "${BLUE}🛰️ Interfaces disponibles:${NC}"
    echo "  📡 Google Earth Engine:     http://localhost:5173/gee"
    echo "  📊 Tableau de bord GEE:     http://localhost:5173/gee-dashboard"
    echo "  🧪 Tests d'intégration:     http://localhost:5173/gee-test"
    echo "  🏠 Tableau de bord principal: http://localhost:5173/dashboard"
    echo ""
    echo -e "${BLUE}📁 Logs:${NC}"
    echo "  🐍 Django: logs/django.log"
    echo "  ⚛️  React:  logs/react.log"
    echo ""
    echo -e "${BLUE}🛑 Pour arrêter:${NC}"
    echo "  ./stop_production.sh"
    echo ""
    echo -e "${GREEN}✅ Système prêt pour la surveillance d'orpaillage !${NC}"
}

# Fonction pour créer le script d'arrêt
create_stop_script() {
    cat > stop_production.sh << 'EOF'
#!/bin/bash

echo "🛑 Arrêt de Gold Sentinel..."

# Arrêter Django
if [ -f "logs/django.pid" ]; then
    DJANGO_PID=$(cat logs/django.pid)
    if kill -0 $DJANGO_PID 2>/dev/null; then
        kill $DJANGO_PID
        echo "✅ Serveur Django arrêté"
    fi
    rm -f logs/django.pid
fi

# Arrêter React
if [ -f "logs/react.pid" ]; then
    REACT_PID=$(cat logs/react.pid)
    if kill -0 $REACT_PID 2>/dev/null; then
        kill $REACT_PID
        echo "✅ Serveur React arrêté"
    fi
    rm -f logs/react.pid
fi

# Arrêter tous les processus liés
pkill -f "python.*manage.py.*runserver" || true
pkill -f "vite.*dev" || true
pkill -f "npm.*start" || true

echo "🎉 Gold Sentinel arrêté"
EOF
    
    chmod +x stop_production.sh
}

# Fonction principale
main() {
    # Créer le dossier logs
    mkdir -p logs
    
    # Vérifications préliminaires
    check_configuration
    
    # Arrêter les processus existants
    stop_existing_processes
    
    # Préparation
    prepare_backend
    prepare_frontend
    
    # Test de connexion
    test_gee_connection
    
    # Démarrage des services
    start_backend
    start_frontend
    
    # Créer le script d'arrêt
    create_stop_script
    
    # Afficher le statut
    show_status
}

# Gestion des signaux pour arrêt propre
trap 'echo -e "\n${YELLOW}🛑 Arrêt en cours...${NC}"; ./stop_production.sh 2>/dev/null; exit 0' INT TERM

# Exécution
main "$@"
