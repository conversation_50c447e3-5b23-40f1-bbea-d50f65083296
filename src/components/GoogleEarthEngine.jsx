import React, { useState, useEffect } from 'react';
import {
  MapIcon,
  CloudArrowDownIcon,
  CpuChipIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  EyeIcon,
  AdjustmentsHorizontalIcon,
  SparklesIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';

const GoogleEarthEngine = ({ user }) => {
  const [selectedRegion, setSelectedRegion] = useState('ZANZAN');
  const [dateRange, setDateRange] = useState({
    start: '2024-01-01',
    end: '2024-12-31'
  });
  const [cloudCoverage, setCloudCoverage] = useState(20);
  const [isLoading, setIsLoading] = useState(false);
  const [images, setImages] = useState([]);
  const [analysisResults, setAnalysisResults] = useState([]);
  const [selectedImage, setSelectedImage] = useState(null);

  // Régions disponibles selon les permissions utilisateur
  const availableRegions = user.niveau_acces === 'NATIONAL' 
    ? ['ZANZAN', 'DENGUELE', 'BOUNKANI', 'BONDOUKOU']
    : user.region_autorisee;

  // Simuler la récupération d'images Sentinel-2
  const fetchSentinelImages = async () => {
    setIsLoading(true);
    
    try {
      // Simulation d'appel à l'API GEE
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockImages = [
        {
          id: 'S2_20241115_ZANZAN_001',
          date: '2024-11-15',
          region: selectedRegion,
          cloudCoverage: 12,
          coordinates: { lat: 8.0402, lng: -2.8000 },
          thumbnail: '/api/placeholder/300/200',
          downloadUrl: '#',
          processed: false,
          detections: 0
        },
        {
          id: 'S2_20241110_ZANZAN_002', 
          date: '2024-11-10',
          region: selectedRegion,
          cloudCoverage: 8,
          coordinates: { lat: 8.1200, lng: -2.7500 },
          thumbnail: '/api/placeholder/300/200',
          downloadUrl: '#',
          processed: true,
          detections: 3
        },
        {
          id: 'S2_20241105_ZANZAN_003',
          date: '2024-11-05', 
          region: selectedRegion,
          cloudCoverage: 15,
          coordinates: { lat: 7.9800, lng: -2.8500 },
          thumbnail: '/api/placeholder/300/200',
          downloadUrl: '#',
          processed: true,
          detections: 1
        }
      ];
      
      setImages(mockImages);
    } catch (error) {
      console.error('Erreur récupération images:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Analyser une image avec l'IA
  const analyzeImage = async (imageId) => {
    const image = images.find(img => img.id === imageId);
    if (!image) return;

    setIsLoading(true);
    
    try {
      // Simulation d'analyse IA
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const mockDetections = [
        {
          id: 1,
          type: 'MINING_SITE',
          confidence: 0.87,
          coordinates: { lat: 8.0420, lng: -2.7980 },
          area: 2.3,
          description: 'Site d\'excavation détecté'
        },
        {
          id: 2,
          type: 'WATER_POLLUTION',
          confidence: 0.72,
          coordinates: { lat: 8.0380, lng: -2.8020 },
          area: 0.8,
          description: 'Pollution de cours d\'eau'
        }
      ];

      // Mettre à jour l'image
      setImages(prev => prev.map(img => 
        img.id === imageId 
          ? { ...img, processed: true, detections: mockDetections.length }
          : img
      ));

      // Ajouter aux résultats d'analyse
      setAnalysisResults(prev => [...prev, {
        imageId,
        date: new Date().toISOString(),
        detections: mockDetections,
        status: 'completed'
      }]);

    } catch (error) {
      console.error('Erreur analyse:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getDetectionIcon = (type) => {
    switch (type) {
      case 'MINING_SITE':
        return '⛏️';
      case 'WATER_POLLUTION':
        return '💧';
      case 'ACCESS_ROAD':
        return '🛣️';
      default:
        return '📍';
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* En-tête */}
      <div className="card-ci">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gradient-ci mb-2">
              🛰️ Google Earth Engine - Surveillance Satellite
            </h1>
            <p className="text-gray-700 text-lg">
              Acquisition et analyse d'images Sentinel-2 pour la détection d'orpaillage
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <SparklesIcon className="h-8 w-8 text-orange-ci-500" />
            <GlobeAltIcon className="h-8 w-8 text-vert-ci-500" />
          </div>
        </div>
      </div>

      {/* Paramètres de recherche */}
      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <AdjustmentsHorizontalIcon className="h-6 w-6 mr-2 text-orange-ci-500" />
          Paramètres de Recherche Sentinel-2
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Région */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Région
            </label>
            <select
              value={selectedRegion}
              onChange={(e) => setSelectedRegion(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-ci-500 focus:border-transparent"
            >
              {availableRegions.map(region => (
                <option key={region} value={region}>{region}</option>
              ))}
            </select>
          </div>

          {/* Date début */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date début
            </label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-ci-500 focus:border-transparent"
            />
          </div>

          {/* Date fin */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date fin
            </label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-ci-500 focus:border-transparent"
            />
          </div>

          {/* Couverture nuageuse */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Couverture nuageuse max: {cloudCoverage}%
            </label>
            <input
              type="range"
              min="0"
              max="50"
              value={cloudCoverage}
              onChange={(e) => setCloudCoverage(e.target.value)}
              className="w-full"
            />
          </div>
        </div>

        <div className="mt-4 flex justify-end">
          <button
            onClick={fetchSentinelImages}
            disabled={isLoading}
            className="btn-primary flex items-center space-x-2"
          >
            {isLoading ? (
              <ClockIcon className="h-5 w-5 animate-spin" />
            ) : (
              <CloudArrowDownIcon className="h-5 w-5" />
            )}
            <span>{isLoading ? 'Recherche...' : 'Rechercher Images'}</span>
          </button>
        </div>
      </div>

      {/* Liste des images */}
      {images.length > 0 && (
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <MapIcon className="h-6 w-6 mr-2 text-vert-ci-500" />
            Images Sentinel-2 Disponibles ({images.length})
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {images.map((image) => (
              <div key={image.id} className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                {/* Thumbnail */}
                <div className="relative">
                  <img
                    src={image.thumbnail}
                    alt={`Sentinel-2 ${image.date}`}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-2 right-2">
                    {image.processed ? (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <CheckCircleIcon className="h-3 w-3 mr-1" />
                        Analysé
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        En attente
                      </span>
                    )}
                  </div>
                </div>

                {/* Informations */}
                <div className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium text-gray-900">{image.id}</h3>
                    <span className="text-sm text-gray-500">{image.date}</span>
                  </div>
                  
                  <div className="space-y-1 text-sm text-gray-600 mb-3">
                    <p>☁️ Nuages: {image.cloudCoverage}%</p>
                    <p>📍 {image.coordinates.lat.toFixed(4)}, {image.coordinates.lng.toFixed(4)}</p>
                    {image.processed && (
                      <p className="text-orange-ci-600 font-medium">
                        🎯 {image.detections} détection(s)
                      </p>
                    )}
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => setSelectedImage(image)}
                      className="flex-1 btn-outline text-sm"
                    >
                      <EyeIcon className="h-4 w-4 mr-1" />
                      Voir
                    </button>
                    
                    {!image.processed && (
                      <button
                        onClick={() => analyzeImage(image.id)}
                        disabled={isLoading}
                        className="flex-1 btn-primary text-sm"
                      >
                        <CpuChipIcon className="h-4 w-4 mr-1" />
                        Analyser
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Résultats d'analyse */}
      {analysisResults.length > 0 && (
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <CpuChipIcon className="h-6 w-6 mr-2 text-orange-ci-500" />
            Résultats d'Analyse IA
          </h2>
          
          <div className="space-y-4">
            {analysisResults.map((result, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-medium text-gray-900">Image: {result.imageId}</h3>
                  <span className="text-sm text-gray-500">
                    {new Date(result.date).toLocaleString('fr-FR')}
                  </span>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {result.detections.map((detection) => (
                    <div key={detection.id} className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-lg">{getDetectionIcon(detection.type)}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(detection.confidence)}`}>
                          {(detection.confidence * 100).toFixed(0)}%
                        </span>
                      </div>
                      
                      <h4 className="font-medium text-gray-900 mb-1">
                        {detection.type.replace('_', ' ')}
                      </h4>
                      <p className="text-sm text-gray-600 mb-2">
                        {detection.description}
                      </p>
                      <div className="text-xs text-gray-500">
                        <p>📍 {detection.coordinates.lat.toFixed(4)}, {detection.coordinates.lng.toFixed(4)}</p>
                        <p>📏 Surface: {detection.area} hectares</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Modal détail image */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Détails - {selectedImage.id}</h2>
                <button
                  onClick={() => setSelectedImage(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
              
              <img
                src={selectedImage.thumbnail}
                alt={selectedImage.id}
                className="w-full h-96 object-cover rounded-lg mb-4"
              />
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p><strong>Date:</strong> {selectedImage.date}</p>
                  <p><strong>Région:</strong> {selectedImage.region}</p>
                  <p><strong>Couverture nuageuse:</strong> {selectedImage.cloudCoverage}%</p>
                </div>
                <div>
                  <p><strong>Coordonnées:</strong> {selectedImage.coordinates.lat.toFixed(4)}, {selectedImage.coordinates.lng.toFixed(4)}</p>
                  <p><strong>Statut:</strong> {selectedImage.processed ? 'Analysé' : 'En attente'}</p>
                  <p><strong>Détections:</strong> {selectedImage.detections}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GoogleEarthEngine;
