import React from 'react';

const TestBasic = () => {
  return (
    <div style={{ padding: '20px', backgroundColor: 'white', minHeight: '100vh' }}>
      <h1 style={{ color: 'orange', fontSize: '32px', marginBottom: '20px' }}>
        🧪 Test Page Basique
      </h1>
      
      <div style={{ 
        backgroundColor: '#f3f4f6', 
        padding: '20px', 
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#374151', marginBottom: '10px' }}>
          ✅ Cette page fonctionne !
        </h2>
        <p style={{ color: '#6b7280' }}>
          Si vous voyez ce texte, React fonctionne correctement.
        </p>
      </div>

      <div style={{ 
        backgroundColor: '#fef3c7', 
        padding: '20px', 
        borderRadius: '8px',
        border: '1px solid #f59e0b',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#92400e', marginBottom: '10px' }}>
          🔧 Diagnostic
        </h3>
        <ul style={{ color: '#78350f', paddingLeft: '20px' }}>
          <li>✅ React Router fonctionne</li>
          <li>✅ Composant rendu</li>
          <li>✅ Styles inline appliqués</li>
          <li>✅ Navigation active</li>
        </ul>
      </div>

      <button 
        onClick={() => alert('Bouton fonctionnel !')}
        style={{
          backgroundColor: '#ea580c',
          color: 'white',
          padding: '12px 24px',
          border: 'none',
          borderRadius: '6px',
          fontSize: '16px',
          cursor: 'pointer'
        }}
      >
        🚀 Tester Interaction
      </button>

      <div style={{ marginTop: '30px', fontSize: '14px', color: '#6b7280' }}>
        <p><strong>URL actuelle:</strong> {window.location.pathname}</p>
        <p><strong>Timestamp:</strong> {new Date().toLocaleString()}</p>
      </div>
    </div>
  );
};

export default TestBasic;
