import React, { useState, useEffect } from 'react';

const GEEDashboard = ({ user }) => {
  const [stats, setStats] = useState({
    totalImages: 0,
    analyzedImages: 0,
    totalDetections: 0,
    highRiskDetections: 0,
    lastUpdate: new Date().toLocaleString()
  });

  const [recentDetections, setRecentDetections] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simuler le chargement des données
    setTimeout(() => {
      setStats({
        totalImages: 127,
        analyzedImages: 89,
        totalDetections: 156,
        highRiskDetections: 23,
        lastUpdate: new Date().toLocaleString()
      });

      setRecentDetections([
        {
          id: 1,
          imageId: 'S2_20241115_ZANZAN_001',
          type: 'MINING_SITE',
          confidence: 0.92,
          location: 'Bondoukou Nord',
          timestamp: '2024-11-15 14:30',
          severity: 'HIGH'
        },
        {
          id: 2,
          imageId: 'S2_20241114_ZANZAN_002',
          type: 'WATER_POLLUTION',
          confidence: 0.78,
          location: 'Rivière Comoé',
          timestamp: '2024-11-14 16:45',
          severity: 'MEDIUM'
        },
        {
          id: 3,
          imageId: 'S2_20241113_ZANZAN_003',
          type: 'ACCESS_ROAD',
          confidence: 0.85,
          location: 'Forêt Classée',
          timestamp: '2024-11-13 09:15',
          severity: 'HIGH'
        }
      ]);

      setIsLoading(false);
    }, 1500);
  }, []);

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'HIGH': return '#dc2626';
      case 'MEDIUM': return '#ea580c';
      case 'LOW': return '#16a34a';
      default: return '#6b7280';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'MINING_SITE': return '⛏️';
      case 'WATER_POLLUTION': return '💧';
      case 'ACCESS_ROAD': return '🛤️';
      case 'DEFORESTATION': return '🌳';
      default: return '🔍';
    }
  };

  if (isLoading) {
    return (
      <div style={{ 
        padding: '20px', 
        backgroundColor: 'white', 
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ 
            fontSize: '48px', 
            marginBottom: '20px',
            animation: 'spin 2s linear infinite'
          }}>
            🛰️
          </div>
          <h2 style={{ color: '#374151', marginBottom: '10px' }}>
            Chargement du Tableau de Bord
          </h2>
          <p style={{ color: '#6b7280' }}>
            Récupération des données satellites en cours...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', backgroundColor: '#f9fafb', minHeight: '100vh' }}>
      {/* En-tête */}
      <div style={{ 
        backgroundColor: 'white', 
        borderRadius: '8px', 
        padding: '20px', 
        marginBottom: '20px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{ color: '#ea580c', fontSize: '28px', marginBottom: '10px' }}>
          📊 Tableau de Bord Google Earth Engine
        </h1>
        <p style={{ color: '#6b7280', marginBottom: '10px' }}>
          Surveillance en temps réel de l'orpaillage illégal - Région {user?.region || 'ZANZAN'}
        </p>
        <p style={{ color: '#9ca3af', fontSize: '14px' }}>
          Dernière mise à jour: {stats.lastUpdate}
        </p>
      </div>

      {/* Métriques principales */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
        gap: '20px',
        marginBottom: '30px'
      }}>
        <div style={{ 
          backgroundColor: 'white', 
          borderRadius: '8px', 
          padding: '20px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          borderLeft: '4px solid #3b82f6'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <h3 style={{ color: '#374151', fontSize: '14px', marginBottom: '5px' }}>
                Images Totales
              </h3>
              <p style={{ color: '#1f2937', fontSize: '32px', fontWeight: 'bold', margin: 0 }}>
                {stats.totalImages}
              </p>
            </div>
            <div style={{ fontSize: '32px' }}>📡</div>
          </div>
        </div>

        <div style={{ 
          backgroundColor: 'white', 
          borderRadius: '8px', 
          padding: '20px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          borderLeft: '4px solid #16a34a'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <h3 style={{ color: '#374151', fontSize: '14px', marginBottom: '5px' }}>
                Images Analysées
              </h3>
              <p style={{ color: '#1f2937', fontSize: '32px', fontWeight: 'bold', margin: 0 }}>
                {stats.analyzedImages}
              </p>
              <p style={{ color: '#16a34a', fontSize: '12px', margin: 0 }}>
                {Math.round((stats.analyzedImages / stats.totalImages) * 100)}% du total
              </p>
            </div>
            <div style={{ fontSize: '32px' }}>🤖</div>
          </div>
        </div>

        <div style={{ 
          backgroundColor: 'white', 
          borderRadius: '8px', 
          padding: '20px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          borderLeft: '4px solid #ea580c'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <h3 style={{ color: '#374151', fontSize: '14px', marginBottom: '5px' }}>
                Détections Totales
              </h3>
              <p style={{ color: '#1f2937', fontSize: '32px', fontWeight: 'bold', margin: 0 }}>
                {stats.totalDetections}
              </p>
            </div>
            <div style={{ fontSize: '32px' }}>🎯</div>
          </div>
        </div>

        <div style={{ 
          backgroundColor: 'white', 
          borderRadius: '8px', 
          padding: '20px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          borderLeft: '4px solid #dc2626'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <h3 style={{ color: '#374151', fontSize: '14px', marginBottom: '5px' }}>
                Alertes Critiques
              </h3>
              <p style={{ color: '#1f2937', fontSize: '32px', fontWeight: 'bold', margin: 0 }}>
                {stats.highRiskDetections}
              </p>
              <p style={{ color: '#dc2626', fontSize: '12px', margin: 0 }}>
                Intervention requise
              </p>
            </div>
            <div style={{ fontSize: '32px' }}>🚨</div>
          </div>
        </div>
      </div>

      {/* Détections récentes */}
      <div style={{ 
        backgroundColor: 'white', 
        borderRadius: '8px', 
        padding: '20px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        marginBottom: '30px'
      }}>
        <h2 style={{ color: '#374151', fontSize: '20px', marginBottom: '20px' }}>
          🔍 Détections Récentes
        </h2>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
          {recentDetections.map((detection) => (
            <div key={detection.id} style={{ 
              border: '1px solid #e5e7eb', 
              borderRadius: '8px', 
              padding: '15px',
              borderLeft: `4px solid ${getSeverityColor(detection.severity)}`
            }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '10px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                  <span style={{ fontSize: '20px' }}>{getTypeIcon(detection.type)}</span>
                  <div>
                    <h3 style={{ color: '#374151', fontSize: '16px', margin: 0 }}>
                      {detection.type.replace('_', ' ')}
                    </h3>
                    <p style={{ color: '#6b7280', fontSize: '14px', margin: 0 }}>
                      {detection.imageId} • {detection.location}
                    </p>
                  </div>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <div style={{ 
                    backgroundColor: getSeverityColor(detection.severity),
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    fontWeight: 'bold',
                    marginBottom: '5px'
                  }}>
                    {detection.severity}
                  </div>
                  <p style={{ color: '#6b7280', fontSize: '12px', margin: 0 }}>
                    {(detection.confidence * 100).toFixed(0)}% confiance
                  </p>
                </div>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <p style={{ color: '#9ca3af', fontSize: '12px', margin: 0 }}>
                  📅 {detection.timestamp}
                </p>
                <div style={{ display: 'flex', gap: '10px' }}>
                  <button 
                    onClick={() => alert(`Détails de la détection:\n\nType: ${detection.type}\nLocalisation: ${detection.location}\nConfiance: ${(detection.confidence * 100).toFixed(1)}%\nSévérité: ${detection.severity}`)}
                    style={{
                      backgroundColor: '#3b82f6',
                      color: 'white',
                      border: 'none',
                      padding: '6px 12px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      cursor: 'pointer'
                    }}
                  >
                    👁️ Voir
                  </button>
                  <button 
                    onClick={() => alert(`Action d'intervention:\n\n📍 Localisation: ${detection.location}\n🚨 Priorité: ${detection.severity}\n📋 Actions recommandées:\n- Vérification terrain\n- Contact autorités locales\n- Surveillance renforcée`)}
                    style={{
                      backgroundColor: '#dc2626',
                      color: 'white',
                      border: 'none',
                      padding: '6px 12px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      cursor: 'pointer'
                    }}
                  >
                    🚨 Intervenir
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Actions rapides */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '15px'
      }}>
        <button 
          onClick={() => window.location.href = '/gee'}
          style={{
            backgroundColor: '#16a34a',
            color: 'white',
            border: 'none',
            padding: '15px',
            borderRadius: '8px',
            fontSize: '16px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '10px'
          }}
        >
          <span>🛰️</span>
          <span>Nouvelle Analyse</span>
        </button>

        <button 
          onClick={() => window.location.href = '/gee-test'}
          style={{
            backgroundColor: '#ea580c',
            color: 'white',
            border: 'none',
            padding: '15px',
            borderRadius: '8px',
            fontSize: '16px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '10px'
          }}
        >
          <span>🧪</span>
          <span>Tests Système</span>
        </button>

        <button 
          onClick={() => alert('Génération du rapport en cours...\n\n📊 Rapport mensuel\n📅 Période: Novembre 2024\n📍 Région: ZANZAN\n📈 Format: PDF')}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '15px',
            borderRadius: '8px',
            fontSize: '16px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '10px'
          }}
        >
          <span>📊</span>
          <span>Générer Rapport</span>
        </button>

        <button 
          onClick={() => alert('Configuration des alertes:\n\n🔔 Notifications temps réel\n📧 Email: Activé\n📱 SMS: Activé\n🎯 Seuil confiance: 80%')}
          style={{
            backgroundColor: '#7c3aed',
            color: 'white',
            border: 'none',
            padding: '15px',
            borderRadius: '8px',
            fontSize: '16px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '10px'
          }}
        >
          <span>⚙️</span>
          <span>Configurer</span>
        </button>
      </div>
    </div>
  );
};

export default GEEDashboard;
