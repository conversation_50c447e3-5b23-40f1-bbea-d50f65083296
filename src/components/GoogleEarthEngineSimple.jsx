import React, { useState } from 'react';

const GoogleEarthEngineSimple = ({ user }) => {
  // Version ultra-simple pour éviter les erreurs
  return (
    <div style={{ padding: '20px', backgroundColor: 'white', minHeight: '100vh' }}>
      <h1 style={{ color: 'orange', fontSize: '32px', marginBottom: '20px' }}>
        🛰️ Google Earth Engine
      </h1>

      <div style={{
        backgroundColor: '#f3f4f6',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#374151', marginBottom: '10px' }}>
          📡 Surveillance par Satellite
        </h2>
        <p style={{ color: '#6b7280' }}>
          Détection d'orpaillage illégal en Côte d'Ivoire via images Sentinel-2
        </p>
        <p style={{ color: '#6b7280', marginTop: '10px' }}>
          <strong>Utilisateur:</strong> {user?.nom || 'Agent'} | <strong>Région:</strong> {user?.region || 'ZANZAN'}
        </p>
      </div>

      <div style={{
        backgroundColor: '#dcfce7',
        padding: '20px',
        borderRadius: '8px',
        border: '1px solid #16a34a',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#15803d', marginBottom: '15px' }}>
          🔍 Recherche d'Images Sentinel-2 (Simulée)
        </h3>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px', marginBottom: '20px' }}>
          <div>
            <label style={{ display: 'block', fontWeight: 'bold', marginBottom: '5px', color: '#166534' }}>
              📍 Région
            </label>
            <select style={{ width: '100%', padding: '8px', border: '1px solid #ccc', borderRadius: '4px' }}>
              <option>ZANZAN</option>
              <option>DENGUELE</option>
              <option>BOUNKANI</option>
            </select>
          </div>

          <div>
            <label style={{ display: 'block', fontWeight: 'bold', marginBottom: '5px', color: '#166534' }}>
              📅 Date début
            </label>
            <input type="date" defaultValue="2024-01-01" style={{ width: '100%', padding: '8px', border: '1px solid #ccc', borderRadius: '4px' }} />
          </div>

          <div>
            <label style={{ display: 'block', fontWeight: 'bold', marginBottom: '5px', color: '#166534' }}>
              📅 Date fin
            </label>
            <input type="date" defaultValue="2024-12-31" style={{ width: '100%', padding: '8px', border: '1px solid #ccc', borderRadius: '4px' }} />
          </div>

          <div>
            <label style={{ display: 'block', fontWeight: 'bold', marginBottom: '5px', color: '#166534' }}>
              ☁️ Nuages max (%)
            </label>
            <input type="number" defaultValue="20" min="0" max="100" style={{ width: '100%', padding: '8px', border: '1px solid #ccc', borderRadius: '4px' }} />
          </div>
        </div>

        <button
          onClick={() => {
            alert('Recherche simulée !\n\n📡 5 images Sentinel-2 trouvées\n📅 Dates: Nov 2024\n☁️ Couverture nuageuse: 5-15%\n📍 Région: ZANZAN');
          }}
          style={{
            backgroundColor: '#16a34a',
            color: 'white',
            padding: '12px 24px',
            border: 'none',
            borderRadius: '6px',
            fontSize: '16px',
            cursor: 'pointer'
          }}
        >
          🔍 Rechercher Images
        </button>
      </div>

      <div style={{
        backgroundColor: '#fef3c7',
        padding: '20px',
        borderRadius: '8px',
        border: '1px solid #f59e0b',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#92400e', marginBottom: '15px' }}>
          📊 Images Disponibles (Simulées)
        </h3>

        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '15px' }}>
          {[
            { id: 'S2_20241115_ZANZAN_001', date: '2024-11-15', clouds: '5%', detections: 3 },
            { id: 'S2_20241110_ZANZAN_002', date: '2024-11-10', clouds: '8%', detections: 1 },
            { id: 'S2_20241105_ZANZAN_003', date: '2024-11-05', clouds: '12%', detections: 0 }
          ].map((img, index) => (
            <div key={index} style={{
              border: '1px solid #d97706',
              borderRadius: '8px',
              padding: '15px',
              backgroundColor: 'white'
            }}>
              <div style={{
                backgroundColor: '#228B22',
                height: '120px',
                borderRadius: '6px',
                marginBottom: '10px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '14px'
              }}>
                🛰️ Sentinel-2<br/>{img.date}
              </div>

              <h4 style={{ fontWeight: 'bold', marginBottom: '8px', color: '#92400e' }}>
                {img.id}
              </h4>
              <p style={{ fontSize: '14px', color: '#78350f', marginBottom: '5px' }}>
                📅 {img.date}
              </p>
              <p style={{ fontSize: '14px', color: '#78350f', marginBottom: '5px' }}>
                ☁️ {img.clouds} nuages
              </p>
              <p style={{ fontSize: '14px', color: '#78350f', marginBottom: '10px' }}>
                🎯 {img.detections} détections
              </p>

              <div style={{ display: 'flex', gap: '8px' }}>
                <button
                  onClick={() => alert(`Détails ${img.id}:\n\n📅 Date: ${img.date}\n☁️ Nuages: ${img.clouds}\n📍 Coordonnées: 8.0402, -2.8000\n🎯 Détections: ${img.detections}`)}
                  style={{
                    backgroundColor: '#2563eb',
                    color: 'white',
                    padding: '6px 12px',
                    border: 'none',
                    borderRadius: '4px',
                    fontSize: '12px',
                    cursor: 'pointer',
                    flex: 1
                  }}
                >
                  👁️ Voir
                </button>
                <button
                  onClick={() => alert(`Analyse IA ${img.id}:\n\n🤖 Modèle: Ghana Detector v2.1\n⏱️ Temps: 45s\n🎯 Détections: ${img.detections}\n✅ Confiance: 87%`)}
                  style={{
                    backgroundColor: '#16a34a',
                    color: 'white',
                    padding: '6px 12px',
                    border: 'none',
                    borderRadius: '4px',
                    fontSize: '12px',
                    cursor: 'pointer',
                    flex: 1
                  }}
                >
                  🤖 Analyser
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div style={{
        backgroundColor: '#dbeafe',
        padding: '20px',
        borderRadius: '8px',
        border: '1px solid #3b82f6'
      }}>
        <h3 style={{ color: '#1d4ed8', marginBottom: '10px' }}>
          📈 Statistiques Régionales
        </h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '15px' }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1d4ed8' }}>25</div>
            <div style={{ fontSize: '14px', color: '#1e40af' }}>Images totales</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1d4ed8' }}>18</div>
            <div style={{ fontSize: '14px', color: '#1e40af' }}>Images analysées</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1d4ed8' }}>42</div>
            <div style={{ fontSize: '14px', color: '#1e40af' }}>Détections totales</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1d4ed8' }}>9</div>
            <div style={{ fontSize: '14px', color: '#1e40af' }}>Haute confiance</div>
          </div>
        </div>
      </div>

      <div style={{ marginTop: '30px', fontSize: '14px', color: '#6b7280' }}>
        <p><strong>Status:</strong> ✅ Interface Fonctionnelle</p>
        <p><strong>Mode:</strong> Simulation (données mockées)</p>
        <p><strong>Dernière mise à jour:</strong> {new Date().toLocaleString()}</p>
      </div>
    </div>
  );
};

export default GoogleEarthEngineSimple;
