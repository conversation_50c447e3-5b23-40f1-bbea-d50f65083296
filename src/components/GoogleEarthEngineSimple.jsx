import React, { useState } from 'react';
import {
  MagnifyingGlassIcon,
  CpuChipIcon,
  EyeIcon,
  CloudIcon,
  CalendarIcon,
  MapPinIcon
} from '@heroicons/react/24/outline';
import googleEarthEngineService from '../services/googleEarthEngineService';

const GoogleEarthEngineSimple = ({ user }) => {
  const [searchParams, setSearchParams] = useState({
    region: 'ZANZAN',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    cloudCoverage: 20
  });
  
  const [images, setImages] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleSearch = async () => {
    setIsSearching(true);
    try {
      const results = await googleEarthEngineService.searchSentinelImages(searchParams);
      setImages(results);
    } catch (error) {
      console.error('Erreur recherche:', error);
      // Fallback vers données mockées
      setImages(googleEarthEngineService.getMockSentinelImages(searchParams.region));
    }
    setIsSearching(false);
  };

  const handleAnalyze = async (imageId) => {
    setIsAnalyzing(true);
    try {
      const result = await googleEarthEngineService.analyzeImage(imageId);
      setAnalysisResult(result);
    } catch (error) {
      console.error('Erreur analyse:', error);
      setAnalysisResult(googleEarthEngineService.getMockAnalysisResults(imageId));
    }
    setIsAnalyzing(false);
  };

  const regions = [
    { value: 'ZANZAN', label: 'Zanzan' },
    { value: 'DENGUELE', label: 'Denguelé' },
    { value: 'BOUNKANI', label: 'Bounkani' },
    { value: 'BONDOUKOU', label: 'Bondoukou' }
  ];

  return (
    <div className="space-y-6 p-6">
      {/* En-tête */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-orange-600 mb-2">
              🛰️ Google Earth Engine
            </h1>
            <p className="text-gray-700 text-lg">
              Surveillance par satellite de l'orpaillage illégal
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600">Utilisateur: {user?.nom || 'Agent'}</p>
            <p className="text-sm text-gray-600">Région: {user?.region || 'ZANZAN'}</p>
          </div>
        </div>
      </div>

      {/* Paramètres de recherche */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
          <MagnifyingGlassIcon className="h-6 w-6 mr-2 text-orange-500" />
          Paramètres de Recherche Sentinel-2
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MapPinIcon className="h-4 w-4 inline mr-1" />
              Région
            </label>
            <select
              value={searchParams.region}
              onChange={(e) => setSearchParams({...searchParams, region: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              {regions.map(region => (
                <option key={region.value} value={region.value}>
                  {region.label}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <CalendarIcon className="h-4 w-4 inline mr-1" />
              Date début
            </label>
            <input
              type="date"
              value={searchParams.startDate}
              onChange={(e) => setSearchParams({...searchParams, startDate: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <CalendarIcon className="h-4 w-4 inline mr-1" />
              Date fin
            </label>
            <input
              type="date"
              value={searchParams.endDate}
              onChange={(e) => setSearchParams({...searchParams, endDate: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <CloudIcon className="h-4 w-4 inline mr-1" />
              Couverture nuageuse max (%)
            </label>
            <input
              type="number"
              min="0"
              max="100"
              value={searchParams.cloudCoverage}
              onChange={(e) => setSearchParams({...searchParams, cloudCoverage: parseInt(e.target.value)})}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>
        </div>
        
        <div className="mt-4">
          <button
            onClick={handleSearch}
            disabled={isSearching}
            className="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white font-semibold px-6 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            {isSearching ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Recherche en cours...</span>
              </>
            ) : (
              <>
                <MagnifyingGlassIcon className="h-4 w-4" />
                <span>Rechercher Images</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Résultats */}
      {images.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            📡 Images Sentinel-2 Disponibles ({images.length})
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {images.map((image, index) => (
              <div key={image.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="aspect-video bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                  <img 
                    src={image.thumbnail} 
                    alt={`Sentinel-2 ${image.date}`}
                    className="w-full h-full object-cover rounded-lg"
                    onError={(e) => {
                      e.target.src = `https://via.placeholder.com/300x200/228B22/FFFFFF?text=Sentinel-2+${image.date}`;
                    }}
                  />
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium text-gray-900">{image.id}</h3>
                  <p className="text-sm text-gray-600">📅 {image.date}</p>
                  <p className="text-sm text-gray-600">☁️ {image.cloudCoverage}% nuages</p>
                  <p className="text-sm text-gray-600">📍 {image.coordinates.lat.toFixed(4)}, {image.coordinates.lng.toFixed(4)}</p>
                  
                  {image.processed && (
                    <p className="text-sm text-green-600 font-medium">
                      ✅ {image.detections} détections
                    </p>
                  )}
                </div>
                
                <div className="mt-4 flex space-x-2">
                  <button
                    onClick={() => setSelectedImage(image)}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-2 rounded-md flex items-center justify-center space-x-1"
                  >
                    <EyeIcon className="h-4 w-4" />
                    <span>Voir</span>
                  </button>
                  
                  <button
                    onClick={() => handleAnalyze(image.id)}
                    disabled={isAnalyzing}
                    className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white text-sm px-3 py-2 rounded-md flex items-center justify-center space-x-1"
                  >
                    <CpuChipIcon className="h-4 w-4" />
                    <span>Analyser</span>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Résultats d'analyse */}
      {analysisResult && (
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            🤖 Résultats d'Analyse IA
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900">Détections Totales</h3>
              <p className="text-2xl font-bold text-blue-600">{analysisResult.totalDetections}</p>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <h3 className="font-medium text-red-900">Haute Confiance</h3>
              <p className="text-2xl font-bold text-red-600">{analysisResult.highConfidenceDetections}</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-medium text-green-900">Temps de Traitement</h3>
              <p className="text-2xl font-bold text-green-600">{analysisResult.processingTime}s</p>
            </div>
          </div>
          
          {analysisResult.detections && analysisResult.detections.length > 0 && (
            <div>
              <h3 className="font-medium text-gray-900 mb-3">Détections Détaillées</h3>
              <div className="space-y-3">
                {analysisResult.detections.map((detection, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900">{detection.type}</span>
                      <span className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {(detection.confidence * 100).toFixed(1)}% confiance
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{detection.description}</p>
                    <p className="text-xs text-gray-500">
                      📍 {detection.coordinates.lat.toFixed(6)}, {detection.coordinates.lng.toFixed(6)}
                    </p>
                    {detection.area && (
                      <p className="text-xs text-gray-500">📏 {detection.area} hectares</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Modal détails image */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Détails - {selectedImage.id}</h3>
              <button
                onClick={() => setSelectedImage(null)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            
            <div className="space-y-4">
              <img 
                src={selectedImage.thumbnail} 
                alt={selectedImage.id}
                className="w-full rounded-lg"
              />
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Date:</strong> {selectedImage.date}
                </div>
                <div>
                  <strong>Région:</strong> {selectedImage.region}
                </div>
                <div>
                  <strong>Nuages:</strong> {selectedImage.cloudCoverage}%
                </div>
                <div>
                  <strong>Résolution:</strong> {selectedImage.resolution}m
                </div>
                <div>
                  <strong>Coordonnées:</strong> {selectedImage.coordinates.lat.toFixed(4)}, {selectedImage.coordinates.lng.toFixed(4)}
                </div>
                <div>
                  <strong>Statut:</strong> {selectedImage.processed ? '✅ Analysé' : '⏳ En attente'}
                </div>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => handleAnalyze(selectedImage.id)}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center space-x-2"
                >
                  <CpuChipIcon className="h-4 w-4" />
                  <span>Analyser avec IA</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GoogleEarthEngineSimple;
