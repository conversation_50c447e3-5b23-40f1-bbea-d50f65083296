import React, { useState } from 'react';

const TestGEEIntegration = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState('');

  const runIntegrationTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    const tests = [
      {
        name: 'Test Service GEE',
        description: 'Vérification du service Google Earth Engine',
        test: async () => {
          // Simulation simple
          await new Promise(resolve => setTimeout(resolve, 1000));
          return {
            success: true,
            message: '5 images trouvées',
            data: ['S2_20241115_ZANZAN_001', 'S2_20241110_ZANZAN_002']
          };
        }
      },
      {
        name: 'Test Analyse IA',
        description: 'Test du modèle de détection d\'orpaillage',
        test: async () => {
          await new Promise(resolve => setTimeout(resolve, 1500));
          return {
            success: true,
            message: '3 détections trouvées',
            data: { detections: ['MINING_SITE', 'WATER_POLLUTION'] }
          };
        }
      },
      {
        name: 'Test Statistiques',
        description: 'Récupération des statistiques régionales',
        test: async () => {
          await new Promise(resolve => setTimeout(resolve, 800));
          return {
            success: true,
            message: '25 images analysées',
            data: { totalImages: 25, detections: 42 }
          };
        }
      },
      {
        name: 'Test Performance',
        description: 'Test de performance des appels API',
        test: async () => {
          const startTime = Date.now();
          await new Promise(resolve => setTimeout(resolve, 500));
          const duration = Date.now() - startTime;

          return {
            success: duration < 5000,
            message: `Temps de réponse: ${duration}ms`,
            data: { duration }
          };
        }
      }
    ];

    for (const test of tests) {
      setCurrentTest(test.name);

      try {
        const result = await test.test();
        setTestResults(prev => [...prev, {
          name: test.name,
          description: test.description,
          ...result,
          timestamp: new Date().toLocaleTimeString('fr-FR')
        }]);

        // Pause entre les tests
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        setTestResults(prev => [...prev, {
          name: test.name,
          description: test.description,
          success: false,
          message: `Erreur inattendue: ${error.message}`,
          data: null,
          timestamp: new Date().toLocaleTimeString('fr-FR')
        }]);
      }
    }

    setCurrentTest('');
    setIsRunning(false);
  };

  const getStatusIcon = (success) => {
    return success ? '✅' : '❌';
  };

  const getStatusColor = (success) => {
    return success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* En-tête */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-orange-600 mb-2">
              🧪 Test d'Intégration Google Earth Engine
            </h1>
            <p className="text-gray-700 text-lg">
              Validation du workflow complet : GEE → IA → Détections
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-2xl">✨</span>
            <span className="text-2xl">🤖</span>
          </div>
        </div>
      </div>

      {/* Bouton de test */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200 text-center">
        <button
          onClick={runIntegrationTests}
          disabled={isRunning}
          className="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white font-semibold text-lg px-8 py-4 rounded-lg flex items-center space-x-3 mx-auto transition-colors"
        >
          {isRunning ? (
            <>
              <span className="animate-spin">⏳</span>
              <span>Tests en cours...</span>
            </>
          ) : (
            <>
              <span>▶️</span>
              <span>Lancer les Tests d'Intégration</span>
            </>
          )}
        </button>

        {isRunning && currentTest && (
          <div className="mt-4 text-orange-600 font-medium">
            🔄 Test en cours: {currentTest}
          </div>
        )}
      </div>

      {/* Résultats des tests */}
      {testResults.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <span className="mr-2">🤖</span>
            Résultats des Tests ({testResults.length})
          </h2>

          <div className="space-y-4">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`border rounded-lg p-4 ${getStatusColor(result.success)}`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(result.success)}
                    <h3 className="font-medium text-gray-900">{result.name}</h3>
                  </div>
                  <span className="text-sm text-gray-500">{result.timestamp}</span>
                </div>

                <p className="text-sm text-gray-600 mb-2">{result.description}</p>

                <div className={`text-sm font-medium ${
                  result.success ? 'text-green-700' : 'text-red-700'
                }`}>
                  {result.message}
                </div>

                {result.data && (
                  <details className="mt-3">
                    <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                      Voir les données détaillées
                    </summary>
                    <pre className="mt-2 text-xs bg-gray-100 p-3 rounded overflow-auto max-h-40">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>

          {/* Résumé */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">📊 Résumé</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Tests réussis:</span>
                <span className="ml-2 font-medium text-green-600">
                  {testResults.filter(r => r.success).length}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Tests échoués:</span>
                <span className="ml-2 font-medium text-red-600">
                  {testResults.filter(r => !r.success).length}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Total:</span>
                <span className="ml-2 font-medium text-gray-900">
                  {testResults.length}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Taux de réussite:</span>
                <span className="ml-2 font-medium text-blue-600">
                  {testResults.length > 0
                    ? Math.round((testResults.filter(r => r.success).length / testResults.length) * 100)
                    : 0}%
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Guide d'utilisation */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          📋 Guide d'Utilisation
        </h2>

        <div className="space-y-4 text-sm text-gray-700">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">🎯 Objectif des Tests</h3>
            <p>
              Ces tests valident l'intégration complète entre le frontend React,
              le service Google Earth Engine, et le modèle IA de détection d'orpaillage.
            </p>
          </div>

          <div>
            <h3 className="font-medium text-gray-900 mb-2">🔍 Tests Effectués</h3>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Recherche d'images Sentinel-2 via Google Earth Engine</li>
              <li>Analyse IA avec le modèle de détection Ghana</li>
              <li>Récupération des statistiques régionales</li>
              <li>Test de performance des appels API</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-gray-900 mb-2">✅ Critères de Réussite</h3>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Images Sentinel-2 récupérées avec succès</li>
              <li>Modèle IA fonctionnel avec détections</li>
              <li>Statistiques cohérentes</li>
              <li>Temps de réponse acceptable (&lt; 5 secondes)</li>
            </ul>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-blue-800">
              <strong>💡 Note:</strong> En mode développement, les tests utilisent des données
              mockées. En production, ils se connecteront aux vraies API Google Earth Engine
              et au modèle IA déployé.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestGEEIntegration;
