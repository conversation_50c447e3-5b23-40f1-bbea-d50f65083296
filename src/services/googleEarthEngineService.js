/**
 * Service pour l'intégration Google Earth Engine
 * Gère la récupération d'images Sentinel-2 et l'analyse IA
 */

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

class GoogleEarthEngineService {
  constructor() {
    this.apiKey = process.env.REACT_APP_GEE_API_KEY;
    this.baseUrl = `${API_BASE_URL}/gee`;
  }

  /**
   * Authentification avec Google Earth Engine
   */
  async authenticate() {
    try {
      const response = await fetch(`${this.baseUrl}/authenticate/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          api_key: this.apiKey
        })
      });

      if (!response.ok) {
        throw new Error('Erreur authentification GEE');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Erreur authentification GEE:', error);
      throw error;
    }
  }

  /**
   * Recherche d'images Sentinel-2
   */
  async searchSentinelImages(params) {
    const {
      region,
      startDate,
      endDate,
      cloudCoverage = 20,
      bounds = null
    } = params;

    try {
      const queryParams = new URLSearchParams({
        region,
        start_date: startDate,
        end_date: endDate,
        cloud_coverage: cloudCoverage,
        ...(bounds && { bounds: JSON.stringify(bounds) })
      });

      const response = await fetch(`${this.baseUrl}/search/?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erreur recherche images Sentinel-2');
      }

      const data = await response.json();
      return data.images || [];
    } catch (error) {
      console.error('Erreur recherche Sentinel-2:', error);
      // Retourner des données mockées en cas d'erreur
      return this.getMockSentinelImages(region);
    }
  }

  /**
   * Télécharger une image Sentinel-2
   */
  async downloadImage(imageId) {
    try {
      const response = await fetch(`${this.baseUrl}/sentinel2/download/${imageId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erreur téléchargement image');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Erreur téléchargement:', error);
      throw error;
    }
  }

  /**
   * Analyser une image avec l'IA
   */
  async analyzeImage(imageId) {
    try {
      const response = await fetch(`${this.baseUrl}/analyze/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          image_id: imageId,
          model_type: 'ghana_detector',
          confidence_threshold: 0.6
        })
      });

      if (!response.ok) {
        throw new Error('Erreur analyse IA');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Erreur analyse IA:', error);
      // Retourner des résultats mockés
      return this.getMockAnalysisResults(imageId);
    }
  }

  /**
   * Obtenir l'historique des analyses
   */
  async getAnalysisHistory(filters = {}) {
    try {
      const queryParams = new URLSearchParams(filters);
      const response = await fetch(`${this.baseUrl}/analysis/history?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erreur récupération historique');
      }

      const data = await response.json();
      return data.analyses || [];
    } catch (error) {
      console.error('Erreur historique:', error);
      return [];
    }
  }

  /**
   * Exporter les résultats d'analyse
   */
  async exportAnalysis(analysisId, format = 'pdf') {
    try {
      const response = await fetch(`${this.baseUrl}/analysis/${analysisId}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ format })
      });

      if (!response.ok) {
        throw new Error('Erreur export analyse');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analysis_${analysisId}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      return true;
    } catch (error) {
      console.error('Erreur export:', error);
      throw error;
    }
  }

  /**
   * Obtenir les statistiques GEE
   */
  async getStatistics(region, period = '30d') {
    try {
      const response = await fetch(`${this.baseUrl}/statistics/?region=${region}&period=${period}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erreur récupération statistiques');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Erreur statistiques:', error);
      return this.getMockStatistics(region);
    }
  }

  /**
   * Données mockées pour les tests
   */
  getMockSentinelImages(region) {
    const baseDate = new Date();
    const images = [];

    for (let i = 0; i < 5; i++) {
      const date = new Date(baseDate);
      date.setDate(date.getDate() - (i * 5));

      images.push({
        id: `S2_${date.toISOString().split('T')[0].replace(/-/g, '')}_${region}_${String(i + 1).padStart(3, '0')}`,
        date: date.toISOString().split('T')[0],
        region: region,
        cloudCoverage: Math.floor(Math.random() * 25),
        coordinates: {
          lat: 8.0402 + (Math.random() - 0.5) * 0.2,
          lng: -2.8000 + (Math.random() - 0.5) * 0.2
        },
        thumbnail: `https://via.placeholder.com/300x200/228B22/FFFFFF?text=Sentinel-2+${date.getDate()}/${date.getMonth() + 1}`,
        downloadUrl: '#',
        processed: Math.random() > 0.6,
        detections: Math.floor(Math.random() * 4),
        size: Math.floor(Math.random() * 100) + 50, // MB
        bands: ['B2', 'B3', 'B4', 'B8', 'B11', 'B12'],
        resolution: 10 // mètres
      });
    }

    return images;
  }

  getMockAnalysisResults(imageId) {
    const detectionTypes = ['MINING_SITE', 'WATER_POLLUTION', 'ACCESS_ROAD', 'DEFORESTATION'];
    const detections = [];

    const numDetections = Math.floor(Math.random() * 5) + 1;

    for (let i = 0; i < numDetections; i++) {
      detections.push({
        id: i + 1,
        type: detectionTypes[Math.floor(Math.random() * detectionTypes.length)],
        confidence: 0.6 + Math.random() * 0.4, // 0.6 à 1.0
        coordinates: {
          lat: 8.0402 + (Math.random() - 0.5) * 0.1,
          lng: -2.8000 + (Math.random() - 0.5) * 0.1
        },
        area: Math.round((Math.random() * 5 + 0.1) * 10) / 10, // 0.1 à 5.1 hectares
        description: this.getDetectionDescription(detectionTypes[Math.floor(Math.random() * detectionTypes.length)]),
        severity: Math.random() > 0.7 ? 'HIGH' : Math.random() > 0.4 ? 'MEDIUM' : 'LOW',
        timestamp: new Date().toISOString()
      });
    }

    return {
      imageId,
      analysisId: `ANALYSIS_${Date.now()}`,
      status: 'completed',
      detections,
      processingTime: Math.floor(Math.random() * 120) + 30, // 30-150 secondes
      modelVersion: 'ghana_detector_v2.1',
      confidence_threshold: 0.6,
      totalDetections: detections.length,
      highConfidenceDetections: detections.filter(d => d.confidence > 0.8).length
    };
  }

  getDetectionDescription(type) {
    const descriptions = {
      'MINING_SITE': 'Site d\'excavation minière détecté',
      'WATER_POLLUTION': 'Pollution de cours d\'eau identifiée',
      'ACCESS_ROAD': 'Route d\'accès non autorisée',
      'DEFORESTATION': 'Zone de déforestation récente'
    };
    return descriptions[type] || 'Anomalie détectée';
  }

  getMockStatistics(region) {
    return {
      region,
      period: '30d',
      totalImages: Math.floor(Math.random() * 50) + 20,
      analyzedImages: Math.floor(Math.random() * 30) + 15,
      totalDetections: Math.floor(Math.random() * 100) + 25,
      highRiskDetections: Math.floor(Math.random() * 20) + 5,
      averageCloudCoverage: Math.floor(Math.random() * 20) + 10,
      lastUpdate: new Date().toISOString(),
      detectionsByType: {
        'MINING_SITE': Math.floor(Math.random() * 30) + 10,
        'WATER_POLLUTION': Math.floor(Math.random() * 20) + 5,
        'ACCESS_ROAD': Math.floor(Math.random() * 25) + 8,
        'DEFORESTATION': Math.floor(Math.random() * 15) + 3
      }
    };
  }

  /**
   * Utilitaires pour les coordonnées
   */
  static getBoundsForRegion(region) {
    const regionBounds = {
      'ZANZAN': {
        north: 8.5,
        south: 7.5,
        east: -2.0,
        west: -3.5
      },
      'DENGUELE': {
        north: 10.5,
        south: 9.0,
        east: -6.5,
        west: -8.0
      },
      'BOUNKANI': {
        north: 9.5,
        south: 8.0,
        east: -2.5,
        west: -4.0
      },
      'BONDOUKOU': {
        north: 8.2,
        south: 7.8,
        east: -2.6,
        west: -3.0
      }
    };

    return regionBounds[region] || regionBounds['ZANZAN'];
  }

  /**
   * Validation des paramètres
   */
  static validateSearchParams(params) {
    const { region, startDate, endDate, cloudCoverage } = params;

    if (!region) {
      throw new Error('Région requise');
    }

    if (!startDate || !endDate) {
      throw new Error('Dates de début et fin requises');
    }

    if (new Date(startDate) > new Date(endDate)) {
      throw new Error('Date de début doit être antérieure à la date de fin');
    }

    if (cloudCoverage < 0 || cloudCoverage > 100) {
      throw new Error('Couverture nuageuse doit être entre 0 et 100%');
    }

    return true;
  }
}

export default new GoogleEarthEngineService();
