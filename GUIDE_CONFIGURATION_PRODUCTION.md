# 🛰️ Guide de Configuration Production - Google Earth Engine

## 🚀 Configuration Automatique (Recommandée)

### **Prérequis**
- Compte Google Cloud avec facturation activée
- Accès administrateur au projet
- Terminal Linux/macOS ou WSL sur Windows

### **Étape 1 : Lancement du Script Automatique**

```bash
# Rendre le script exécutable (si pas déjà fait)
chmod +x setup_gee_production.sh

# Lancer la configuration automatique
./setup_gee_production.sh
```

Le script va automatiquement :
- ✅ Installer Google Cloud CLI (si nécessaire)
- ✅ Vous authentifier avec Google Cloud
- ✅ Créer le projet `gold-sentinel-gee`
- ✅ Activer les APIs nécessaires
- ✅ Créer le compte de service
- ✅ Attribuer les rôles appropriés
- ✅ Générer la clé JSON
- ✅ Configurer les variables d'environnement
- ✅ Installer les dépendances Python
- ✅ Tester la configuration

### **Étape 2 : Vérification**

```bash
# Tester la configuration complète
python test_gee_real.py

# Démarrer le backend
cd gold-sentinel
source venv/bin/activate
python manage.py runserver

# Dans un autre terminal, démarrer le frontend
npm start
```

### **Étape 3 : Test de l'Interface**

Ouvrez votre navigateur et allez sur :
- **Interface GEE** : http://localhost:5173/gee
- **Tableau de bord** : http://localhost:5173/gee-dashboard
- **Tests** : http://localhost:5173/gee-test

---

## 🔧 Configuration Manuelle (Alternative)

### **1. Créer le Projet Google Cloud**

```bash
# Installer Google Cloud CLI
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Authentification
gcloud auth login

# Créer le projet
gcloud projects create gold-sentinel-gee --name="Gold Sentinel GEE"
gcloud config set project gold-sentinel-gee
```

### **2. Activer les APIs**

```bash
gcloud services enable earthengine.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable iam.googleapis.com
gcloud services enable cloudresourcemanager.googleapis.com
```

### **3. Créer le Compte de Service**

```bash
# Créer le compte de service
gcloud iam service-accounts create gold-sentinel-service \
    --display-name="Gold Sentinel GEE Service Account"

# Attribuer les rôles
gcloud projects add-iam-policy-binding gold-sentinel-gee \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/earthengine.viewer"

gcloud projects add-iam-policy-binding gold-sentinel-gee \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/earthengine.writer"

# Créer la clé JSON
mkdir -p gold-sentinel/secrets
gcloud iam service-accounts keys create gold-sentinel/secrets/gee-service-account.json \
    --iam-account=<EMAIL>
```

### **4. Configuration des Variables**

```bash
# Backend (.env dans gold-sentinel/)
cat > gold-sentinel/.env << EOF
GEE_PROJECT_ID=gold-sentinel-gee
GEE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GEE_SERVICE_ACCOUNT_KEY_PATH=secrets/gee-service-account.json
DJANGO_SECRET_KEY=$(python -c 'from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())')
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
EOF

# Frontend (.env dans la racine)
cat > .env << EOF
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_GEE_PROJECT_ID=gold-sentinel-gee
REACT_APP_ENVIRONMENT=development
EOF
```

### **5. Installation des Dépendances**

```bash
# Backend Python
cd gold-sentinel
python3 -m venv venv
source venv/bin/activate
echo "earthengine-api==0.1.384" >> requirements.txt
pip install -r requirements.txt

# Frontend Node.js
cd ..
npm install
```

---

## 🧪 Tests et Validation

### **Test Automatique**

```bash
# Test complet de la configuration
python test_gee_real.py
```

### **Test Manuel**

```python
# Test d'authentification
import ee
import json

with open('gold-sentinel/secrets/gee-service-account.json', 'r') as f:
    service_account_info = json.load(f)

credentials = ee.ServiceAccountCredentials(
    service_account_info['client_email'],
    'gold-sentinel/secrets/gee-service-account.json'
)

ee.Initialize(credentials, project='gold-sentinel-gee')

# Test d'accès aux données
geometry = ee.Geometry.Rectangle([-3.0, 7.8, -2.6, 8.2])  # Bondoukou
collection = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED').filterBounds(geometry).limit(1)
print(f"Images trouvées: {collection.size().getInfo()}")
```

---

## 🚨 Dépannage

### **Erreur d'Authentification**

```bash
# Réauthentification
gcloud auth login
gcloud auth application-default login

# Vérifier les permissions
gcloud projects get-iam-policy gold-sentinel-gee
```

### **Erreur API Non Activée**

```bash
# Vérifier les APIs activées
gcloud services list --enabled

# Réactiver si nécessaire
gcloud services enable earthengine.googleapis.com
```

### **Erreur de Quota**

```bash
# Vérifier les quotas
gcloud compute project-info describe --project=gold-sentinel-gee
```

### **Erreur de Clé de Service**

```bash
# Régénérer la clé
gcloud iam service-accounts keys create gold-sentinel/secrets/gee-service-account.json \
    --iam-account=<EMAIL>
```

---

## 📊 Monitoring et Maintenance

### **Surveillance des Quotas**

```bash
# Vérifier l'utilisation Earth Engine
gcloud logging read "resource.type=gce_instance AND logName=projects/gold-sentinel-gee/logs/earthengine"
```

### **Rotation des Clés**

```bash
# Créer une nouvelle clé
gcloud iam service-accounts keys create new-key.json \
    --iam-account=<EMAIL>

# Supprimer l'ancienne clé (après test)
gcloud iam service-accounts keys delete KEY_ID \
    --iam-account=<EMAIL>
```

### **Backup de Configuration**

```bash
# Sauvegarder la configuration
tar -czf gold-sentinel-config-backup.tar.gz \
    gold-sentinel/.env \
    gold-sentinel/secrets/ \
    .env
```

---

## 🎯 Optimisations Production

### **Cache et Performance**

```python
# Dans settings.py
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Cache des résultats GEE
GEE_CACHE_TIMEOUT = 3600  # 1 heure
```

### **Logging Avancé**

```python
# Configuration logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'gee_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/gee.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
        },
    },
    'loggers': {
        'gee': {
            'handlers': ['gee_file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

---

## ✅ Checklist de Déploiement

- [ ] Projet Google Cloud créé
- [ ] APIs Earth Engine activées
- [ ] Compte de service configuré
- [ ] Clé JSON générée et sécurisée
- [ ] Variables d'environnement définies
- [ ] Dépendances installées
- [ ] Tests automatiques réussis
- [ ] Interface web fonctionnelle
- [ ] Monitoring configuré
- [ ] Backup de configuration effectué

---

## 🎉 Félicitations !

Votre système Gold Sentinel est maintenant configuré avec les vraies API Google Earth Engine et prêt pour la production ! 🛰️🇨🇮
