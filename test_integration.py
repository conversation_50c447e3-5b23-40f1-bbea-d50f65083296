#!/usr/bin/env python3
"""
Script de test d'intégration complète
Teste le workflow : Google Earth Engine → Backend Django → Frontend React
"""

import os
import sys
import requests
import json
import time
from datetime import datetime, timedelta

class IntegrationTester:
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.test_results = {}
        
    def test_backend_health(self):
        """Test la santé du backend Django"""
        print("🔍 Test de santé du backend...")
        
        try:
            response = requests.get(f"{self.backend_url}/api/health/", timeout=10)
            if response.status_code == 200:
                print("✅ Backend Django accessible")
                return True
            else:
                print(f"❌ Backend inaccessible: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Erreur connexion backend: {e}")
            return False
    
    def test_frontend_health(self):
        """Test la santé du frontend React"""
        print("🔍 Test de santé du frontend...")
        
        try:
            response = requests.get(self.frontend_url, timeout=10)
            if response.status_code == 200:
                print("✅ Frontend React accessible")
                return True
            else:
                print(f"❌ Frontend inaccessible: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Erreur connexion frontend: {e}")
            return False
    
    def test_gee_integration(self):
        """Test l'intégration Google Earth Engine"""
        print("🛰️ Test de l'intégration Google Earth Engine...")
        
        test_params = {
            "region": "ZANZAN",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "cloud_coverage": 20
        }
        
        try:
            # Test de recherche d'images Sentinel-2
            response = requests.get(
                f"{self.backend_url}/api/gee/sentinel2/search",
                params=test_params,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                images = data.get('images', [])
                print(f"✅ GEE: {len(images)} images Sentinel-2 trouvées")
                return True
            else:
                print(f"❌ GEE: Erreur {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur GEE: {e}")
            return False
    
    def test_ai_model(self):
        """Test le modèle IA de détection"""
        print("🤖 Test du modèle IA...")
        
        test_data = {
            "image_id": "S2_20241115_ZANZAN_001",
            "model_type": "ghana_detector",
            "confidence_threshold": 0.6
        }
        
        try:
            response = requests.post(
                f"{self.backend_url}/api/gee/analyze",
                json=test_data,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                detections = data.get('detections', [])
                print(f"✅ IA: {len(detections)} détections trouvées")
                return True
            else:
                print(f"❌ IA: Erreur {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur IA: {e}")
            return False
    
    def test_authentication(self):
        """Test l'authentification"""
        print("🔐 Test de l'authentification...")
        
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        try:
            response = requests.post(
                f"{self.backend_url}/api/auth/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                token = data.get('token')
                if token:
                    print("✅ Authentification réussie")
                    return token
                else:
                    print("❌ Token manquant")
                    return None
            else:
                print(f"❌ Authentification échouée: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Erreur authentification: {e}")
            return None
    
    def test_api_endpoints(self, token):
        """Test les endpoints API principaux"""
        print("🔗 Test des endpoints API...")
        
        headers = {"Authorization": f"Bearer {token}"} if token else {}
        
        endpoints = [
            "/api/regions/",
            "/api/images/",
            "/api/detections/",
            "/api/alerts/",
            "/api/reports/"
        ]
        
        success_count = 0
        for endpoint in endpoints:
            try:
                response = requests.get(
                    f"{self.backend_url}{endpoint}",
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code in [200, 401]:  # 401 acceptable si pas de token
                    print(f"✅ {endpoint}: OK")
                    success_count += 1
                else:
                    print(f"❌ {endpoint}: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {endpoint}: {e}")
        
        return success_count == len(endpoints)
    
    def test_database_connection(self):
        """Test la connexion à la base de données"""
        print("🗄️ Test de la base de données...")
        
        try:
            response = requests.get(
                f"{self.backend_url}/api/health/db",
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ Base de données accessible")
                return True
            else:
                print(f"❌ Base de données inaccessible: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur base de données: {e}")
            return False
    
    def test_frontend_components(self):
        """Test les composants frontend"""
        print("⚛️ Test des composants frontend...")
        
        # Vérifier que les fichiers existent
        components = [
            "src/components/GoogleEarthEngine.jsx",
            "src/services/googleEarthEngineService.js",
            "src/components/Dashboard.jsx",
            "src/components/MapViewSimple.jsx"
        ]
        
        success_count = 0
        for component in components:
            if os.path.exists(component):
                print(f"✅ {component}: Existe")
                success_count += 1
            else:
                print(f"❌ {component}: Manquant")
        
        return success_count == len(components)
    
    def run_performance_test(self):
        """Test de performance"""
        print("⚡ Test de performance...")
        
        start_time = time.time()
        
        # Test de charge simple
        try:
            for i in range(5):
                response = requests.get(f"{self.backend_url}/api/health/", timeout=5)
                if response.status_code != 200:
                    print(f"❌ Échec requête {i+1}")
                    return False
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ Performance: 5 requêtes en {duration:.2f}s")
            return duration < 10  # Moins de 10 secondes acceptable
            
        except Exception as e:
            print(f"❌ Erreur performance: {e}")
            return False
    
    def generate_report(self):
        """Génère un rapport de test"""
        print("\n" + "="*60)
        print("📊 RAPPORT DE TEST D'INTÉGRATION")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        print(f"Tests réussis: {passed_tests}/{total_tests}")
        print(f"Taux de réussite: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\nDétail des tests:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {test_name}: {status}")
        
        if passed_tests == total_tests:
            print("\n🎉 Tous les tests sont passés ! L'intégration est fonctionnelle.")
        else:
            print(f"\n⚠️ {total_tests - passed_tests} test(s) ont échoué. Vérifiez la configuration.")
        
        # Recommandations
        print("\n📋 RECOMMANDATIONS:")
        if not self.test_results.get("Backend Health", False):
            print("  - Démarrer le serveur Django: python manage.py runserver")
        if not self.test_results.get("Frontend Health", False):
            print("  - Démarrer le serveur React: npm start")
        if not self.test_results.get("Database", False):
            print("  - Vérifier la configuration PostgreSQL/PostGIS")
        if not self.test_results.get("GEE Integration", False):
            print("  - Configurer les clés API Google Earth Engine")
        
        return passed_tests == total_tests
    
    def run_all_tests(self):
        """Lance tous les tests"""
        print("🚀 Démarrage des tests d'intégration Gold Sentinel")
        print("="*60)
        
        # Tests de base
        self.test_results["Backend Health"] = self.test_backend_health()
        self.test_results["Frontend Health"] = self.test_frontend_health()
        self.test_results["Frontend Components"] = self.test_frontend_components()
        
        # Tests avancés si le backend est accessible
        if self.test_results["Backend Health"]:
            self.test_results["Database"] = self.test_database_connection()
            token = self.test_authentication()
            self.test_results["Authentication"] = token is not None
            self.test_results["API Endpoints"] = self.test_api_endpoints(token)
            self.test_results["GEE Integration"] = self.test_gee_integration()
            self.test_results["AI Model"] = self.test_ai_model()
            self.test_results["Performance"] = self.run_performance_test()
        
        # Génération du rapport
        return self.generate_report()

if __name__ == "__main__":
    print("🛰️ Gold Sentinel - Test d'Intégration Complète")
    print("Surveillance d'Orpaillage avec Google Earth Engine + IA")
    print()
    
    tester = IntegrationTester()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)
