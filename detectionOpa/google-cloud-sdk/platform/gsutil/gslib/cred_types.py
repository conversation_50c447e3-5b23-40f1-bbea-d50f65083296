# -*- coding: utf-8 -*-
# Copyright 2013 Google Inc. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Defines gsutil-supported credential types."""

from __future__ import absolute_import
from __future__ import print_function
from __future__ import division
from __future__ import unicode_literals


class CredTypes(object):
  HMAC = "HMAC"
  IMPERSONATION = "Service Account Impersonation"
  OAUTH2_SERVICE_ACCOUNT = "OAuth 2.0 Service Account"
  OAUTH2_USER_ACCOUNT = "Oauth 2.0 User Account"
  GCE = "GCE"
  DEVSHELL = "Google Developer Shell"
  EXTERNAL_ACCOUNT = "External Account"
  EXTERNAL_ACCOUNT_AUTHORIZED_USER = "External Account Authorized User"
