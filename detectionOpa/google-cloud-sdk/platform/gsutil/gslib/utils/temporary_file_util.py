# -*- coding: utf-8 -*-
# Copyright 2021 Google Inc. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Utils for temporary files."""

from __future__ import absolute_import
from __future__ import print_function
from __future__ import division
from __future__ import unicode_literals


def GetTempFileName(storage_url):
  """Returns temporary file name for uncompressed file."""
  return '%s_.gstmp' % storage_url.object_name


def GetTempZipFileName(storage_url):
  """Returns temporary name for a temporarily compressed file."""
  return '%s_.gztmp' % storage_url.object_name


def GetStetTempFileName(storage_url):
  """Returns temporary file name for result of STET transform."""
  return '%s_.stet_tmp' % storage_url.object_name
