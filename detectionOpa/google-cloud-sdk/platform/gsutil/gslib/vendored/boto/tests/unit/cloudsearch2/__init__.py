DEMO_DOMAIN_DATA = {
    "SearchInstanceType": None,
    "DomainId": "1234567890/demo",
    "DomainName": "demo",
    "Deleted": False,
    "SearchInstanceCount": 0,
    "Created": True,
    "SearchService": {
        "Endpoint": "search-demo.us-east-1.cloudsearch.amazonaws.com"
    },
    "RequiresIndexDocuments": Fals<PERSON>,
    "Processing": False,
    "DocService": {
        "Endpoint": "doc-demo.us-east-1.cloudsearch.amazonaws.com"
    },
    "ARN": "arn:aws:cs:us-east-1:1234567890:domain/demo",
    "SearchPartitionCount": 0
}
