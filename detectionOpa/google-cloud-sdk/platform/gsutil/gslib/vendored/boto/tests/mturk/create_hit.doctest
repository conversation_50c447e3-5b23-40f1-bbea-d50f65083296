>>> import uuid
>>> import datetime
>>> from _init_environment import MTurkConnection, mturk_host
>>> from boto.mturk.question import Question, QuestionContent, AnswerSpecification, FreeTextAnswer

>>> conn = MTurkConnection(host=mturk_host)

# create content for a question
>>> qn_content = QuestionContent()
>>> qn_content.append_field('Title', 'Boto no hit type question content')
>>> qn_content.append_field('Text', 'What is a boto no hit type?')

# create the question specification
>>> qn = Question(identifier=str(uuid.uuid4()),
...               content=qn_content,
...               answer_spec=AnswerSpecification(FreeTextAnswer()))

# now, create the actual HIT for the question without using a HIT type
# NOTE - the response_groups are specified to get back additional information for testing
>>> keywords=['boto', 'test', 'doctest']
>>> lifetime = datetime.timedelta(minutes=65)
>>> create_hit_rs = conn.create_hit(question=qn,
...                                 lifetime=lifetime,
...                                 max_assignments=2,
...                                 title='Boto create_hit title',
...                                 description='Boto create_hit description',
...                                 keywords=keywords,
...                                 reward=0.23,
...                                 duration=60*6,
...                                 approval_delay=60*60,
...                                 annotation='An annotation from boto create_hit test',
...                                 response_groups=['Minimal',
...                                                  'HITDetail',
...                                                  'HITQuestion',
...                                                  'HITAssignmentSummary',])

# this is a valid request
>>> create_hit_rs.status
True

>>> len(create_hit_rs)
1
>>> hit = create_hit_rs[0]

# for the requested hit type id
# the HIT Type Id is a unicode string
>>> hit_type_id = hit.HITTypeId
>>> hit_type_id # doctest: +ELLIPSIS
u'...'

>>> hit.MaxAssignments
u'2'

>>> hit.AutoApprovalDelayInSeconds
u'3600'

# expiration should be very close to now + the lifetime
>>> expected_datetime = datetime.datetime.now(tz=datetime.timezone.utc).replace(tzinfo=None) + lifetime
>>> expiration_datetime = datetime.datetime.strptime(hit.Expiration, '%Y-%m-%dT%H:%M:%SZ')
>>> delta = expected_datetime - expiration_datetime
>>> abs(delta).seconds < 5
True

# duration is as specified for the HIT type
>>> hit.AssignmentDurationInSeconds
u'360'

# the reward has been set correctly (allow for float error here)
>>> int(float(hit.Amount) * 100)
23

>>> hit.FormattedPrice
u'$0.23'

# only US currency supported at present
>>> hit.CurrencyCode
u'USD'

# title is the HIT type title
>>> hit.Title
u'Boto create_hit title'

# title is the HIT type description
>>> hit.Description
u'Boto create_hit description'

# annotation is correct
>>> hit.RequesterAnnotation
u'An annotation from boto create_hit test'

>>> hit.HITReviewStatus
u'NotReviewed'
