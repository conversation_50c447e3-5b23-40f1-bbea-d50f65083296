# Copyright (c) 2006,2007 <PERSON> http://garnaat.org/
# Copyright (c) 2014 Amazon.com, Inc. All rights reserved.
#
# Permission is hereby granted, free of charge, to any person obtaining a
# copy of this software and associated documentation files (the
# "Software"), to deal in the Software without restriction, including
# without limitation the rights to use, copy, modify, merge, publish, dis-
# tribute, sublicense, and/or sell copies of the Software, and to permit
# persons to whom the Software is furnished to do so, subject to the fol-
# lowing conditions:
#
# The above copyright notice and this permission notice shall be included
# in all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
# OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABIL-
# ITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT
# SHALL THE AUTHOR BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, 
# WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
# IN THE SOFTWARE.

"""
Represents an SQS MessageAttribute Name/Value set
"""

class MessageAttributes(dict):
    def __init__(self, parent):
        self.parent = parent
        self.current_key = None
        self.current_value = None

    def startElement(self, name, attrs, connection):
        if name == 'Value':
            self.current_value = MessageAttributeValue(self)
            return self.current_value

    def endElement(self, name, value, connection):
        if name == 'MessageAttribute':
            self[self.current_key] = self.current_value
        elif name == 'Name':
            self.current_key = value
        elif name == 'Value':
            pass
        else:
            setattr(self, name, value)


class MessageAttributeValue(dict):
    def __init__(self, parent):
        self.parent = parent

    def startElement(self, name, attrs, connection):
        pass

    def endElement(self, name, value, connection):
        if name == 'DataType':
            self['data_type'] = value
        elif name == 'StringValue':
            self['string_value'] = value
        elif name == 'BinaryValue':
            self['binary_value'] = value
        elif name == 'StringListValue':
            self['string_list_value'] = value
        elif name == 'BinaryListValue':
            self['binary_list_value'] = value
