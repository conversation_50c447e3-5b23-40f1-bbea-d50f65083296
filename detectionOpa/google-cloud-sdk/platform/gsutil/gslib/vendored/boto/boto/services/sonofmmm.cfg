#
# Your AWS Credentials
# You only need to supply these in this file if you are not using
# the boto tools to start your service
#
#[Credentials]
#aws_access_key_id = <AWS Access Key Here>
#aws_secret_access_key = <AWS Secret Key Here>

#
# Fill out this section if you want emails from the service
# when it starts and stops
#
#[Notification]
#smtp_host = <your smtp host>
#smtp_user = <your smtp username, if necessary>
#smtp_pass = <your smtp password, if necessary>
#smtp_from = <email address for From: field>
#smtp_to = <email address for To: field>

[Pyami]
scripts = boto.services.sonofmmm.SonOfMMM

[SonOfMMM]
# id of the AMI to be launched
ami_id = ami-dc799cb5
# number of times service will read an empty queue before exiting
# a negative value will cause the service to run forever
retry_count = 5
# seconds to wait after empty queue read before reading again
loop_delay = 10
# average time it takes to process a transaction
# controls invisibility timeout of messages
processing_time = 60
ffmpeg_args = -y -i %%s -f mov -r 29.97 -b 1200kb -mbd 2 -flags +4mv+trell -aic 2 -cmp 2 -subcmp 2 -ar 48000 -ab 19200 -s 320x240 -vcodec mpeg4 -acodec libfaac %%s
output_mimetype = video/quicktime
output_ext = .mov
input_bucket = <S3 bucket where source videos live>
output_bucket = <S3 bucket where converted videos should be stored>
output_domain = <SimpleDB domain to store results - optional>
output_queue = <SQS queue to store results - optional>
input_queue = <SQS queue where work to be done will be queued up>

