#
# Your AWS Credentials
#
[Credentials]
aws_access_key_id = <AWS Access Key Here>
aws_secret_access_key = <AWS Secret Key Here>

#
# If you want to use a separate set of credentials when writing
# to the destination bucket, put them here
#dest_aws_access_key_id = <AWS Access Key Here>
#dest_aws_secret_access_key = <AWS Secret Key Here>

#
# Fill out this section if you want emails from CopyBot
# when it starts and stops
#
[Notification]
#smtp_host = <your smtp host>
#smtp_user = <your smtp username, if necessary>
#smtp_pass = <your smtp password, if necessary>
#smtp_from = <email address for From: field>
#smtp_to = <email address for To: field>

#
# If you leave this section as is, it will automatically
# update boto from subversion upon start up.
# If you don't want that to happen, comment this out
#
[Boto]
boto_location = /usr/local/boto
boto_update = svn:HEAD

#
# This tells the Pyami code in boto what scripts
# to run during startup
#
[Pyami]
scripts = boto.pyami.copybot.CopyBot

#
# Source bucket and Destination Bucket, obviously.
# If the Destination bucket does not exist, it will
# attempt to create it.
# If exit_on_completion is false, the instance
# will keep running after the copy operation is
# complete which might be handy for debugging.
# If copy_acls is false, the ACL's will not be
# copied with the objects to the new bucket.
# If replace_dst is false, copybot will not
# will only store the source file in the dest if
# that file does not already exist.  If it's true
# it will replace it even if it does exist.
#
[CopyBot]
src_bucket = <your source bucket name>
dst_bucket = <your destination bucket name>
exit_on_completion = true
copy_acls = true
replace_dst = true
