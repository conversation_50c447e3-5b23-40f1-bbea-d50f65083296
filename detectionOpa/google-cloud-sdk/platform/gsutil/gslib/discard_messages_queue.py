# -*- coding: utf-8 -*-
# Copyright 2018 Google Inc. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Provides a message queue that discards all messages."""

from __future__ import absolute_import
from __future__ import print_function
from __future__ import division
from __future__ import unicode_literals


class DiscardMessagesQueue(object):
  """Emulates a Cloud API status queue but drops all messages.

  This is useful when you want to perform some operations but not have the UI
  thread display information about those ops (e.g. running a test or fetching
  the public gsutil tarball object's metadata to perform a version check).
  """

  # pylint: disable=invalid-name, unused-argument
  def put(self, message=None, timeout=None):
    pass

  # pylint: enable=invalid-name, unused-argument
