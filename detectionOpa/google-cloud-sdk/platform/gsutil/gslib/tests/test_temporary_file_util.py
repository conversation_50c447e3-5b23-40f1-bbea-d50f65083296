# -*- coding: utf-8 -*-
# Copyright 2021 Google Inc. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Tests for temporary_file_util.py."""

from __future__ import absolute_import
from __future__ import print_function
from __future__ import division
from __future__ import unicode_literals

from gslib import storage_url
from gslib.tests import testcase
from gslib.utils import temporary_file_util


class TestTemporaryUtil(testcase.GsUtilUnitTestCase):
  """Test temporary file utils."""

  def testGetsTemporaryFileName(self):
    self.assertEqual(
        temporary_file_util.GetTempFileName(
            storage_url.StorageUrlFromString('file.txt')), 'file.txt_.gstmp')

  def testGetsTemporaryZipFileName(self):
    self.assertEqual(
        temporary_file_util.GetTempZipFileName(
            storage_url.StorageUrlFromString('file.txt')), 'file.txt_.gztmp')

  def testGetsStetTemporaryFileName(self):
    self.assertEqual(
        temporary_file_util.GetStetTempFileName(
            storage_url.StorageUrlFromString('file.txt')), 'file.txt_.stet_tmp')
