# Copyright 2014 Google Inc. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Generated message classes for storage version v1.

Stores and retrieves potentially large, immutable data objects.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from apitools.base.protorpclite import message_types as _message_types
from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types

package = 'storage'


class Bucket(_messages.Message):
  r"""A bucket.

  Messages:
    AutoclassValue: The bucket's Autoclass configuration.
    BillingValue: The bucket's billing configuration.
    CorsValueListEntry: A CorsValueListEntry object.
    CustomPlacementConfigValue: The bucket's custom placement configuration
      for Custom Dual Regions.
    EncryptionValue: Encryption configuration for a bucket.
    IamConfigurationValue: The bucket's IAM configuration.
    LabelsValue: User-provided labels, in key/value pairs.
    LifecycleValue: The bucket's lifecycle configuration. See lifecycle
      management for more information.
    LoggingValue: The bucket's logging configuration, which defines the
      destination bucket and optional name prefix for the current bucket's
      logs.
    OwnerValue: The owner of the bucket. This is always the project team's
      owner group.
    RetentionPolicyValue: The bucket's retention policy. The retention policy
      enforces a minimum retention time for all objects contained in the
      bucket, based on their creation time. Any attempt to overwrite or delete
      objects younger than the retention period will result in a
      PERMISSION_DENIED error. An unlocked retention policy can be modified or
      removed from the bucket via a storage.buckets.update operation. A locked
      retention policy cannot be removed or shortened in duration for the
      lifetime of the bucket. Attempting to remove or decrease period of a
      locked retention policy will result in a PERMISSION_DENIED error.
    VersioningValue: The bucket's versioning configuration.
    WebsiteValue: The bucket's website configuration, controlling how the
      service behaves when accessing bucket contents as a web site. See the
      Static Website Examples for more information.

  Fields:
    acl: Access controls on the bucket.
    autoclass: The bucket's Autoclass configuration.
    billing: The bucket's billing configuration.
    cors: The bucket's Cross-Origin Resource Sharing (CORS) configuration.
    customPlacementConfig: The bucket's custom placement configuration for
      Custom Dual Regions.
    defaultEventBasedHold: The default value for event-based hold on newly
      created objects in this bucket. Event-based hold is a way to retain
      objects indefinitely until an event occurs, signified by the hold's
      release. After being released, such objects will be subject to bucket-
      level retention (if any). One sample use case of this flag is for banks
      to hold loan documents for at least 3 years after loan is paid in full.
      Here, bucket-level retention is 3 years and the event is loan being paid
      in full. In this example, these objects will be held intact for any
      number of years until the event has occurred (event-based hold on the
      object is released) and then 3 more years after that. That means
      retention duration of the objects begins from the moment event-based
      hold transitioned from true to false. Objects under event-based hold
      cannot be deleted, overwritten or archived until the hold is removed.
    defaultObjectAcl: Default access controls to apply to new objects when no
      ACL is provided.
    encryption: Encryption configuration for a bucket.
    etag: HTTP 1.1 Entity tag for the bucket.
    iamConfiguration: The bucket's IAM configuration.
    id: The ID of the bucket. For buckets, the id and name properties are the
      same.
    kind: The kind of item this is. For buckets, this is always
      storage#bucket.
    labels: User-provided labels, in key/value pairs.
    lifecycle: The bucket's lifecycle configuration. See lifecycle management
      for more information.
    location: The location of the bucket. Object data for objects in the
      bucket resides in physical storage within this region. Defaults to US.
      See the developer's guide for the authoritative list.
    locationType: The type of the bucket location.
    logging: The bucket's logging configuration, which defines the destination
      bucket and optional name prefix for the current bucket's logs.
    metageneration: The metadata generation of this bucket.
    name: The name of the bucket.
    owner: The owner of the bucket. This is always the project team's owner
      group.
    projectNumber: The project number of the project the bucket belongs to.
    retentionPolicy: The bucket's retention policy. The retention policy
      enforces a minimum retention time for all objects contained in the
      bucket, based on their creation time. Any attempt to overwrite or delete
      objects younger than the retention period will result in a
      PERMISSION_DENIED error. An unlocked retention policy can be modified or
      removed from the bucket via a storage.buckets.update operation. A locked
      retention policy cannot be removed or shortened in duration for the
      lifetime of the bucket. Attempting to remove or decrease period of a
      locked retention policy will result in a PERMISSION_DENIED error.
    rpo: The Recovery Point Objective (RPO) of this bucket. Set to
      ASYNC_TURBO to turn on turbo replication on a bucket.
    satisfiesPZS: Reserved for future use.
    selfLink: The URI of this bucket.
    storageClass: The bucket's default storage class, used whenever no
      storageClass is specified for a newly-created object. This defines how
      objects in the bucket are stored and determines the SLA and the cost of
      storage. Values include MULTI_REGIONAL, REGIONAL, STANDARD, NEARLINE,
      COLDLINE, ARCHIVE, and DURABLE_REDUCED_AVAILABILITY. If this value is
      not specified when the bucket is created, it will default to STANDARD.
      For more information, see storage classes.
    timeCreated: The creation time of the bucket in RFC 3339 format.
    updated: The modification time of the bucket in RFC 3339 format.
    versioning: The bucket's versioning configuration.
    website: The bucket's website configuration, controlling how the service
      behaves when accessing bucket contents as a web site. See the Static
      Website Examples for more information.
  """

  class AutoclassValue(_messages.Message):
    r"""The bucket's Autoclass configuration.

    Fields:
      enabled: Whether or not Autoclass is enabled on this bucket
      toggleTime: A date and time in RFC 3339 format representing the instant
        at which "enabled" was last toggled.
    """

    enabled = _messages.BooleanField(1)
    toggleTime = _message_types.DateTimeField(2)

  class BillingValue(_messages.Message):
    r"""The bucket's billing configuration.

    Fields:
      requesterPays: When set to true, Requester Pays is enabled for this
        bucket.
    """

    requesterPays = _messages.BooleanField(1)

  class CorsValueListEntry(_messages.Message):
    r"""A CorsValueListEntry object.

    Fields:
      maxAgeSeconds: The value, in seconds, to return in the  Access-Control-
        Max-Age header used in preflight responses.
      method: The list of HTTP methods on which to include CORS response
        headers, (GET, OPTIONS, POST, etc) Note: "*" is permitted in the list
        of methods, and means "any method".
      origin: The list of Origins eligible to receive CORS response headers.
        Note: "*" is permitted in the list of origins, and means "any Origin".
      responseHeader: The list of HTTP headers other than the simple response
        headers to give permission for the user-agent to share across domains.
    """

    maxAgeSeconds = _messages.IntegerField(1, variant=_messages.Variant.INT32)
    method = _messages.StringField(2, repeated=True)
    origin = _messages.StringField(3, repeated=True)
    responseHeader = _messages.StringField(4, repeated=True)

  class CustomPlacementConfigValue(_messages.Message):
    r"""The bucket's custom placement configuration for Custom Dual Regions.

    Fields:
      dataLocations: The list of regional locations in which data is placed.
    """

    dataLocations = _messages.StringField(1, repeated=True)

  class EncryptionValue(_messages.Message):
    r"""Encryption configuration for a bucket.

    Fields:
      defaultKmsKeyName: A Cloud KMS key that will be used to encrypt objects
        inserted into this bucket, if no encryption method is specified.
    """

    defaultKmsKeyName = _messages.StringField(1)

  class IamConfigurationValue(_messages.Message):
    r"""The bucket's IAM configuration.

    Messages:
      BucketPolicyOnlyValue: The bucket's Bucket Policy Only configuration.

    Fields:
      bucketPolicyOnly: The bucket's Bucket Policy Only configuration.
      publicAccessPrevention: The bucket's public access prevention
        configuration. Valid values are `unspecified` and `enforced`.
    """

    class BucketPolicyOnlyValue(_messages.Message):
      r"""The bucket's Bucket Policy Only configuration.

      Fields:
        enabled: If set, access checks only use bucket-level IAM policies or
          above.
        lockedTime: The deadline time for changing
          iamConfiguration.bucketPolicyOnly.enabled from true to false in RFC
          3339 format. iamConfiguration.bucketPolicyOnly.enabled may be
          changed from true to false until the locked time, after which the
          field is immutable.
      """

      enabled = _messages.BooleanField(1)
      lockedTime = _message_types.DateTimeField(2)

    bucketPolicyOnly = _messages.MessageField('BucketPolicyOnlyValue', 1)
    publicAccessPrevention = _messages.StringField(2)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-provided labels, in key/value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: An individual label entry.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty',
                                                  1,
                                                  repeated=True)

  class LifecycleValue(_messages.Message):
    r"""The bucket's lifecycle configuration. See lifecycle management for
    more information.

    Messages:
      RuleValueListEntry: A RuleValueListEntry object.

    Fields:
      rule: A lifecycle management rule, which is made of an action to take
        and the condition(s) under which the action will be taken.
    """

    class RuleValueListEntry(_messages.Message):
      r"""A RuleValueListEntry object.

      Messages:
        ActionValue: The action to take.
        ConditionValue: The condition(s) under which the action will be taken.

      Fields:
        action: The action to take.
        condition: The condition(s) under which the action will be taken.
      """

      class ActionValue(_messages.Message):
        r"""The action to take.

        Fields:
          storageClass: Target storage class. Required iff the type of the
            action is SetStorageClass.
          type: Type of the action. Currently, only Delete and SetStorageClass
            are supported.
        """

        storageClass = _messages.StringField(1)
        type = _messages.StringField(2)

      class ConditionValue(_messages.Message):
        r"""The condition(s) under which the action will be taken.

        Fields:
          age: Age of an object (in days). This condition is satisfied when an
            object reaches the specified age.
          createdBefore: A date in RFC 3339 format with only the date part
            (for instance, "2013-01-15"). This condition is satisfied when an
            object is created before midnight of the specified date in UTC.
          customTimeBefore: A date in RFC 3339 format with only the date part
            (for instance, "2013-01-15"). This condition is satisfied when the
            custom time on an object is before this date in UTC.
          daysSinceCustomTime: Number of days elapsed since the user-specified
            timestamp set on an object. The condition is satisfied if the days
            elapsed is at least this number. If no custom timestamp is
            specified on an object, the condition does not apply.
          daysSinceNoncurrentTime: Number of days elapsed since the noncurrent
            timestamp of an object. The condition is satisfied if the days
            elapsed is at least this number. This condition is relevant only
            for versioned objects. The value of the field must be a
            nonnegative integer. If it's zero, the object version will become
            eligible for Lifecycle action as soon as it becomes noncurrent.
          isLive: Relevant only for versioned objects. If the value is true,
            this condition matches live objects; if the value is false, it
            matches archived objects.
          matchesStorageClass: Objects having any of the storage classes
            specified by this condition will be matched. Values include
            MULTI_REGIONAL, REGIONAL, NEARLINE, COLDLINE, ARCHIVE, STANDARD,
            and DURABLE_REDUCED_AVAILABILITY.
          noncurrentTimeBefore: A date in RFC 3339 format with only the date
            part (for instance, "2013-01-15"). This condition is satisfied
            when the noncurrent time on an object is before this date in UTC.
            This condition is relevant only for versioned objects.
          numNewerVersions: Relevant only for versioned objects. If the value
            is N, this condition is satisfied when there are at least N
            versions (including the live version) newer than this version of
            the object.
        """

        age = _messages.IntegerField(1, variant=_messages.Variant.INT32)
        createdBefore = extra_types.DateField(2)
        customTimeBefore = extra_types.DateField(6)
        daysSinceCustomTime = _messages.IntegerField(
            7, variant=_messages.Variant.INT32)
        daysSinceNoncurrentTime = _messages.IntegerField(
            8, variant=_messages.Variant.INT32)
        isLive = _messages.BooleanField(3)
        matchesStorageClass = _messages.StringField(4, repeated=True)
        noncurrentTimeBefore = extra_types.DateField(9)
        numNewerVersions = _messages.IntegerField(
            5, variant=_messages.Variant.INT32)

      action = _messages.MessageField('ActionValue', 1)
      condition = _messages.MessageField('ConditionValue', 2)

    rule = _messages.MessageField('RuleValueListEntry', 1, repeated=True)

  class LoggingValue(_messages.Message):
    r"""The bucket's logging configuration, which defines the destination
    bucket and optional name prefix for the current bucket's logs.

    Fields:
      logBucket: The destination bucket where the current bucket's logs should
        be placed.
      logObjectPrefix: A prefix for log object names.
    """

    logBucket = _messages.StringField(1)
    logObjectPrefix = _messages.StringField(2)

  class OwnerValue(_messages.Message):
    r"""The owner of the bucket. This is always the project team's owner
    group.

    Fields:
      entity: The entity, in the form project-owner-projectId.
      entityId: The ID for the entity.
    """

    entity = _messages.StringField(1)
    entityId = _messages.StringField(2)

  class RetentionPolicyValue(_messages.Message):
    r"""The bucket's retention policy. The retention policy enforces a minimum
    retention time for all objects contained in the bucket, based on their
    creation time. Any attempt to overwrite or delete objects younger than the
    retention period will result in a PERMISSION_DENIED error. An unlocked
    retention policy can be modified or removed from the bucket via a
    storage.buckets.update operation. A locked retention policy cannot be
    removed or shortened in duration for the lifetime of the bucket.
    Attempting to remove or decrease period of a locked retention policy will
    result in a PERMISSION_DENIED error.

    Fields:
      effectiveTime: Server-determined value that indicates the time from
        which policy was enforced and effective. This value is in RFC 3339
        format.
      isLocked: Once locked, an object retention policy cannot be modified.
      retentionPeriod: The duration in seconds that objects need to be
        retained. Retention duration must be greater than zero and less than
        100 years. Note that enforcement of retention periods less than a day
        is not guaranteed. Such periods should only be used for testing
        purposes.
    """

    effectiveTime = _message_types.DateTimeField(1)
    isLocked = _messages.BooleanField(2)
    retentionPeriod = _messages.IntegerField(3)

  class VersioningValue(_messages.Message):
    r"""The bucket's versioning configuration.

    Fields:
      enabled: While set to true, versioning is fully enabled for this bucket.
    """

    enabled = _messages.BooleanField(1)

  class WebsiteValue(_messages.Message):
    r"""The bucket's website configuration, controlling how the service
    behaves when accessing bucket contents as a web site. See the Static
    Website Examples for more information.

    Fields:
      mainPageSuffix: If the requested object path is missing, the service
        will ensure the path has a trailing '/', append this suffix, and
        attempt to retrieve the resulting object. This allows the creation of
        index.html objects to represent directory pages.
      notFoundPage: If the requested object path is missing, and any
        mainPageSuffix object is missing, if applicable, the service will
        return the named object from this bucket as the content for a 404 Not
        Found result.
    """

    mainPageSuffix = _messages.StringField(1)
    notFoundPage = _messages.StringField(2)

  acl = _messages.MessageField('BucketAccessControl', 1, repeated=True)
  autoclass = _messages.MessageField('AutoclassValue', 2)
  billing = _messages.MessageField('BillingValue', 3)
  cors = _messages.MessageField('CorsValueListEntry', 4, repeated=True)
  customPlacementConfig = _messages.MessageField('CustomPlacementConfigValue',
                                                 5)
  defaultEventBasedHold = _messages.BooleanField(6)
  defaultObjectAcl = _messages.MessageField('ObjectAccessControl', 7, repeated=True)
  encryption = _messages.MessageField('EncryptionValue', 8)
  etag = _messages.StringField(9)
  iamConfiguration = _messages.MessageField('IamConfigurationValue', 10)
  id = _messages.StringField(11)
  kind = _messages.StringField(12, default=u'storage#bucket')
  labels = _messages.MessageField('LabelsValue', 13)
  lifecycle = _messages.MessageField('LifecycleValue', 14)
  location = _messages.StringField(15)
  locationType = _messages.StringField(16)
  logging = _messages.MessageField('LoggingValue', 17)
  metageneration = _messages.IntegerField(18)
  name = _messages.StringField(19)
  owner = _messages.MessageField('OwnerValue', 20)
  projectNumber = _messages.IntegerField(21, variant=_messages.Variant.UINT64)
  retentionPolicy = _messages.MessageField('RetentionPolicyValue', 22)
  rpo = _messages.StringField(23)
  satisfiesPZS = _messages.BooleanField(24)
  selfLink = _messages.StringField(25)
  storageClass = _messages.StringField(26)
  timeCreated = _message_types.DateTimeField(27)
  updated = _message_types.DateTimeField(28)
  versioning = _messages.MessageField('VersioningValue', 29)
  website = _messages.MessageField('WebsiteValue', 30)


class BucketAccessControl(_messages.Message):
  r"""An access-control entry.

  Messages:
    ProjectTeamValue: The project team associated with the entity, if any.

  Fields:
    bucket: The name of the bucket.
    domain: The domain associated with the entity, if any.
    email: The email address associated with the entity, if any.
    entity: The entity holding the permission, in one of the following forms:
      - user-userId  - user-email  - group-groupId  - group-email  - domain-
      domain  - project-team-projectId  - allUsers  - allAuthenticatedUsers
      Examples:  - <NAME_EMAIL> <NAME_EMAIL>.  -
      <NAME_EMAIL> would be group-
      <EMAIL>.  - To refer to all members of the Google Apps
      for Business domain example.com, the entity would be domain-example.com.
    entityId: The ID for the entity, if any.
    etag: HTTP 1.1 Entity tag for the access-control entry.
    id: The ID of the access-control entry.
    kind: The kind of item this is. For bucket access control entries, this is
      always storage#bucketAccessControl.
    projectTeam: The project team associated with the entity, if any.
    role: The access permission for the entity.
    selfLink: The link to this access-control entry.
  """

  class ProjectTeamValue(_messages.Message):
    r"""The project team associated with the entity, if any.

    Fields:
      projectNumber: The project number.
      team: The team.
    """

    projectNumber = _messages.StringField(1)
    team = _messages.StringField(2)

  bucket = _messages.StringField(1)
  domain = _messages.StringField(2)
  email = _messages.StringField(3)
  entity = _messages.StringField(4)
  entityId = _messages.StringField(5)
  etag = _messages.StringField(6)
  id = _messages.StringField(7)
  kind = _messages.StringField(8, default=u'storage#bucketAccessControl')
  projectTeam = _messages.MessageField('ProjectTeamValue', 9)
  role = _messages.StringField(10)
  selfLink = _messages.StringField(11)


class BucketAccessControls(_messages.Message):
  r"""An access-control list.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of bucket access control
      entries, this is always storage#bucketAccessControls.
  """

  items = _messages.MessageField('BucketAccessControl', 1, repeated=True)
  kind = _messages.StringField(2, default=u'storage#bucketAccessControls')


class Buckets(_messages.Message):
  r"""A list of buckets.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of buckets, this is always
      storage#buckets.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
  """

  items = _messages.MessageField('Bucket', 1, repeated=True)
  kind = _messages.StringField(2, default=u'storage#buckets')
  nextPageToken = _messages.StringField(3)


class Channel(_messages.Message):
  r"""An notification channel used to watch for resource changes.

  Messages:
    ParamsValue: Additional parameters controlling delivery channel behavior.
      Optional.

  Fields:
    address: The address where notifications are delivered for this channel.
    expiration: Date and time of notification channel expiration, expressed as
      a Unix timestamp, in milliseconds. Optional.
    id: A UUID or similar unique string that identifies this channel.
    kind: Identifies this as a notification channel used to watch for changes
      to a resource. Value: the fixed string "api#channel".
    params: Additional parameters controlling delivery channel behavior.
      Optional.
    payload: A Boolean value to indicate whether payload is wanted. Optional.
    resourceId: An opaque ID that identifies the resource being watched on
      this channel. Stable across different API versions.
    resourceUri: A version-specific identifier for the watched resource.
    token: An arbitrary string delivered to the target address with each
      notification delivered over this channel. Optional.
    type: The type of delivery mechanism used for this channel.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParamsValue(_messages.Message):
    r"""Additional parameters controlling delivery channel behavior. Optional.

    Messages:
      AdditionalProperty: An additional property for a ParamsValue object.

    Fields:
      additionalProperties: Declares a new parameter by name.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParamsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty',
                                                  1,
                                                  repeated=True)

  address = _messages.StringField(1)
  expiration = _messages.IntegerField(2)
  id = _messages.StringField(3)
  kind = _messages.StringField(4, default=u'api#channel')
  params = _messages.MessageField('ParamsValue', 5)
  payload = _messages.BooleanField(6)
  resourceId = _messages.StringField(7)
  resourceUri = _messages.StringField(8)
  token = _messages.StringField(9)
  type = _messages.StringField(10)


class Channels(_messages.Message):
  r"""A channels list response.

  Messages:
    ItemsValueListEntry: A ItemsValueListEntry object.

  Fields:
    items: The list of notification channels for a bucket.
    kind: The kind of item this is.
  """

  class ItemsValueListEntry(_messages.Message):
    r"""A ItemsValueListEntry object.

    Fields:
      channel_id: User-specified name for a channel. Needed to unsubscribe.
      creation_time_ms: 64-bit Unix timestamp in ms of when the channel was
        created.
      push_url: Url used to identify where notifications are sent to.
      resource_id: Opaque value generated by GCS representing a bucket. Needed
        to unsubscribe.
      subscriber_email: Email address of the subscriber.
    """

    channel_id = _messages.StringField(1)
    creation_time_ms = _messages.IntegerField(2)
    push_url = _messages.StringField(3)
    resource_id = _messages.StringField(4)
    subscriber_email = _messages.StringField(5)

  items = _messages.MessageField('ItemsValueListEntry', 1, repeated=True)
  kind = _messages.StringField(2, default=u'storage#channels')


class ComposeRequest(_messages.Message):
  r"""A Compose request.

  Messages:
    SourceObjectsValueListEntry: A SourceObjectsValueListEntry object.

  Fields:
    destination: Properties of the resulting object.
    kind: The kind of item this is.
    sourceObjects: The list of source objects that will be concatenated into a
      single object.
    userProject: The project to be billed for this request, for Requester Pays
      buckets.
  """

  class SourceObjectsValueListEntry(_messages.Message):
    r"""A SourceObjectsValueListEntry object.

    Messages:
      ObjectPreconditionsValue: Conditions that must be met for this operation
        to execute.

    Fields:
      generation: The generation of this object to use as the source.
      name: The source object's name. All source objects must reside in the
        same bucket.
      objectPreconditions: Conditions that must be met for this operation to
        execute.
    """

    class ObjectPreconditionsValue(_messages.Message):
      r"""Conditions that must be met for this operation to execute.

      Fields:
        ifGenerationMatch: Only perform the composition if the generation of
          the source object that would be used matches this value. If this
          value and a generation are both specified, they must be the same
          value or the call will fail.
      """

      ifGenerationMatch = _messages.IntegerField(1)

    generation = _messages.IntegerField(1)
    name = _messages.StringField(2)
    objectPreconditions = _messages.MessageField('ObjectPreconditionsValue', 3)

  destination = _messages.MessageField('Object', 1)
  kind = _messages.StringField(2, default=u'storage#composeRequest')
  sourceObjects = _messages.MessageField('SourceObjectsValueListEntry',
                                         3,
                                         repeated=True)
  userProject = _messages.StringField(4)


class Expr(_messages.Message):
  r"""Represents an expression text. Example: title: "User account presence"
  description: "Determines whether the request has a user account" expression:
  "size(request.user) > 0"

  Fields:
    description: An optional description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax. The application context of the containing message
      determines which well-known feature set of CEL is supported.
    kind: The kind of item this is. For storage, this is always storage#expr.
      This field is ignored on input.
    location: An optional string indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: An optional title for the expression, i.e. a short string
      describing its purpose. This can be used e.g. in UIs which allow to
      enter the expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  kind = _messages.StringField(3, default=u'storage#expr')
  location = _messages.StringField(4)
  title = _messages.StringField(5)


class Notification(_messages.Message):
  r"""A subscription to receive Google PubSub notifications.

  Messages:
    CustomAttributesValue: An optional list of additional attributes to attach
      to each Cloud PubSub message published for this notification
      subscription.

  Fields:
    custom_attributes: An optional list of additional attributes to attach to
      each Cloud PubSub message published for this notification subscription.
    etag: HTTP 1.1 Entity tag for this subscription notification.
    event_types: If present, only send notifications about listed event types.
      If empty, sent notifications for all event types.
    id: The ID of the notification.
    kind: The kind of item this is. For notifications, this is always
      storage#notification.
    object_name_prefix: If present, only apply this notification configuration
      to object names that begin with this prefix.
    payload_format: The desired content of the Payload.
    selfLink: The canonical URL of this notification.
    topic: The Cloud PubSub topic to which this subscription publishes.
      Formatted as: '//pubsub.googleapis.com/projects/{project-
      identifier}/topics/{my-topic}'
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomAttributesValue(_messages.Message):
    r"""An optional list of additional attributes to attach to each Cloud
    PubSub message published for this notification subscription.

    Messages:
      AdditionalProperty: An additional property for a CustomAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        CustomAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty',
                                                  1,
                                                  repeated=True)

  custom_attributes = _messages.MessageField('CustomAttributesValue', 1)
  etag = _messages.StringField(2)
  event_types = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)
  kind = _messages.StringField(5, default=u'storage#notification')
  object_name_prefix = _messages.StringField(6)
  payload_format = _messages.StringField(7, default=u'JSON_API_V1')
  selfLink = _messages.StringField(8)
  topic = _messages.StringField(9)


class Notifications(_messages.Message):
  r"""A list of notification subscriptions.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of notifications, this is always
      storage#notifications.
  """

  items = _messages.MessageField('Notification', 1, repeated=True)
  kind = _messages.StringField(2, default=u'storage#notifications')


class Object(_messages.Message):
  r"""An object.

  Messages:
    CustomerEncryptionValue: Metadata of customer-supplied encryption key, if
      the object is encrypted by such a key.
    MetadataValue: User-provided metadata, in key/value pairs.
    OwnerValue: The owner of the object. This will always be the uploader of
      the object.

  Fields:
    acl: Access controls on the object.
    bucket: The name of the bucket containing this object.
    cacheControl: Cache-Control directive for the object data. If omitted, and
      the object is accessible to all anonymous users, the default will be
      public, max-age=3600.
    componentCount: Number of underlying components that make up this object.
      Components are accumulated by compose operations.
    contentDisposition: Content-Disposition of the object data.
    contentEncoding: Content-Encoding of the object data.
    contentLanguage: Content-Language of the object data.
    contentType: Content-Type of the object data. If an object is stored
      without a Content-Type, it is served as application/octet-stream.
    crc32c: CRC32c checksum, as described in RFC 4960, Appendix B; encoded
      using base64 in big-endian byte order. For more information about using
      the CRC32c checksum, see Hashes and ETags: Best Practices.
    customTime: A timestamp in RFC 3339 format specified by the user for an
      object.
    customerEncryption: Metadata of customer-supplied encryption key, if the
      object is encrypted by such a key.
    etag: HTTP 1.1 Entity tag for the object.
    eventBasedHold: Whether an object is under event-based hold. Event-based
      hold is a way to retain objects until an event occurs, which is
      signified by the hold's release (i.e. this value is set to false). After
      being released (set to false), such objects will be subject to bucket-
      level retention (if any). One sample use case of this flag is for banks
      to hold loan documents for at least 3 years after loan is paid in full.
      Here, bucket-level retention is 3 years and the event is the loan being
      paid in full. In this example, these objects will be held intact for any
      number of years until the event has occurred (event-based hold on the
      object is released) and then 3 more years after that. That means
      retention duration of the objects begins from the moment event-based
      hold transitioned from true to false.
    generation: The content generation of this object. Used for object
      versioning.
    id: The ID of the object, including the bucket name, object name, and
      generation number.
    kind: The kind of item this is. For objects, this is always
      storage#object.
    kmsKeyName: Cloud KMS Key used to encrypt this object, if the object is
      encrypted by such a key.
    md5Hash: MD5 hash of the data; encoded using base64. For more information
      about using the MD5 hash, see Hashes and ETags: Best Practices.
    mediaLink: Media download link.
    metadata: User-provided metadata, in key/value pairs.
    metageneration: The version of the metadata for this object at this
      generation. Used for preconditions and for detecting changes in
      metadata. A metageneration number is only meaningful in the context of a
      particular generation of a particular object.
    name: The name of the object. Required if not specified by URL parameter.
    owner: The owner of the object. This will always be the uploader of the
      object.
    retentionExpirationTime: A server-determined value that specifies the
      earliest time that the object's retention period expires. This value is
      in RFC 3339 format. Note 1: This field is not provided for objects with
      an active event-based hold, since retention expiration is unknown until
      the hold is removed. Note 2: This value can be provided even when
      temporary hold is set (so that the user can reason about policy without
      having to first unset the temporary hold).
    selfLink: The link to this object.
    size: Content-Length of the data in bytes.
    storageClass: Storage class of the object.
    temporaryHold: Whether an object is under temporary hold. While this flag
      is set to true, the object is protected against deletion and overwrites.
      A common use case of this flag is regulatory investigations where
      objects need to be retained while the investigation is ongoing. Note
      that unlike event-based hold, temporary hold does not impact retention
      expiration time of an object.
    timeCreated: The creation time of the object in RFC 3339 format.
    timeDeleted: The deletion time of the object in RFC 3339 format. Will be
      returned if and only if this version of the object has been deleted.
    timeStorageClassUpdated: The time at which the object's storage class was
      last changed. When the object is initially created, it will be set to
      timeCreated.
    updated: The modification time of the object metadata in RFC 3339 format.
  """

  class CustomerEncryptionValue(_messages.Message):
    r"""Metadata of customer-supplied encryption key, if the object is
    encrypted by such a key.

    Fields:
      encryptionAlgorithm: The encryption algorithm.
      keySha256: SHA256 hash value of the encryption key.
    """

    encryptionAlgorithm = _messages.StringField(1)
    keySha256 = _messages.StringField(2)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""User-provided metadata, in key/value pairs.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: An individual metadata entry.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty',
                                                  1,
                                                  repeated=True)

  class OwnerValue(_messages.Message):
    r"""The owner of the object. This will always be the uploader of the
    object.

    Fields:
      entity: The entity, in the form user-userId.
      entityId: The ID for the entity.
    """

    entity = _messages.StringField(1)
    entityId = _messages.StringField(2)

  acl = _messages.MessageField('ObjectAccessControl', 1, repeated=True)
  bucket = _messages.StringField(2)
  cacheControl = _messages.StringField(3)
  componentCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  contentDisposition = _messages.StringField(5)
  contentEncoding = _messages.StringField(6)
  contentLanguage = _messages.StringField(7)
  contentType = _messages.StringField(8)
  crc32c = _messages.StringField(9)
  customTime = _message_types.DateTimeField(32)
  customerEncryption = _messages.MessageField('CustomerEncryptionValue', 10)
  etag = _messages.StringField(11)
  eventBasedHold = _messages.BooleanField(12)
  generation = _messages.IntegerField(13)
  id = _messages.StringField(14)
  kind = _messages.StringField(15, default=u'storage#object')
  kmsKeyName = _messages.StringField(16)
  md5Hash = _messages.StringField(17)
  mediaLink = _messages.StringField(18)
  metadata = _messages.MessageField('MetadataValue', 19)
  metageneration = _messages.IntegerField(20)
  name = _messages.StringField(21)
  owner = _messages.MessageField('OwnerValue', 22)
  retentionExpirationTime = _message_types.DateTimeField(23)
  selfLink = _messages.StringField(24)
  size = _messages.IntegerField(25, variant=_messages.Variant.UINT64)
  storageClass = _messages.StringField(26)
  temporaryHold = _messages.BooleanField(27)
  timeCreated = _message_types.DateTimeField(28)
  timeDeleted = _message_types.DateTimeField(29)
  timeStorageClassUpdated = _message_types.DateTimeField(30)
  updated = _message_types.DateTimeField(31)


class ObjectAccessControl(_messages.Message):
  r"""An access-control entry.

  Messages:
    ProjectTeamValue: The project team associated with the entity, if any.

  Fields:
    bucket: The name of the bucket.
    domain: The domain associated with the entity, if any.
    email: The email address associated with the entity, if any.
    entity: The entity holding the permission, in one of the following forms:
      - user-userId  - user-email  - group-groupId  - group-email  - domain-
      domain  - project-team-projectId  - allUsers  - allAuthenticatedUsers
      Examples:  - <NAME_EMAIL> <NAME_EMAIL>.  -
      <NAME_EMAIL> would be group-
      <EMAIL>.  - To refer to all members of the Google Apps
      for Business domain example.com, the entity would be domain-example.com.
    entityId: The ID for the entity, if any.
    etag: HTTP 1.1 Entity tag for the access-control entry.
    generation: The content generation of the object, if applied to an object.
    id: The ID of the access-control entry.
    kind: The kind of item this is. For object access control entries, this is
      always storage#objectAccessControl.
    object: The name of the object, if applied to an object.
    projectTeam: The project team associated with the entity, if any.
    role: The access permission for the entity.
    selfLink: The link to this access-control entry.
  """

  class ProjectTeamValue(_messages.Message):
    r"""The project team associated with the entity, if any.

    Fields:
      projectNumber: The project number.
      team: The team.
    """

    projectNumber = _messages.StringField(1)
    team = _messages.StringField(2)

  bucket = _messages.StringField(1)
  domain = _messages.StringField(2)
  email = _messages.StringField(3)
  entity = _messages.StringField(4)
  entityId = _messages.StringField(5)
  etag = _messages.StringField(6)
  generation = _messages.IntegerField(7)
  id = _messages.StringField(8)
  kind = _messages.StringField(9, default=u'storage#objectAccessControl')
  object = _messages.StringField(10)
  projectTeam = _messages.MessageField('ProjectTeamValue', 11)
  role = _messages.StringField(12)
  selfLink = _messages.StringField(13)


class ObjectAccessControls(_messages.Message):
  r"""An access-control list.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of object access control
      entries, this is always storage#objectAccessControls.
  """

  items = _messages.MessageField('ObjectAccessControl', 1, repeated=True)
  kind = _messages.StringField(2, default=u'storage#objectAccessControls')


class Objects(_messages.Message):
  r"""A list of objects.

  Fields:
    items: The list of items.
    kind: The kind of item this is. For lists of objects, this is always
      storage#objects.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
    prefixes: The list of prefixes of objects matching-but-not-listed up to
      and including the requested delimiter.
  """

  items = _messages.MessageField('Object', 1, repeated=True)
  kind = _messages.StringField(2, default=u'storage#objects')
  nextPageToken = _messages.StringField(3)
  prefixes = _messages.StringField(4, repeated=True)


class Policy(_messages.Message):
  r"""A bucket/object IAM policy.

  Messages:
    BindingsValueListEntry: A BindingsValueListEntry object.

  Fields:
    bindings: An association between a role, which comes with a set of
      permissions, and members who may assume that role.
    etag: HTTP 1.1  Entity tag for the policy.
    kind: The kind of item this is. For policies, this is always
      storage#policy. This field is ignored on input.
    resourceId: The ID of the resource to which this policy belongs. Will be
      of the form projects/_/buckets/bucket for buckets, and
      projects/_/buckets/bucket/objects/object for objects. A specific
      generation may be specified by appending #generationNumber to the end of
      the object name, e.g. projects/_/buckets/my-bucket/objects/data.txt#17.
      The current generation can be denoted with #0. This field is ignored on
      input.
    version: The policy format version to be returned in the response.
  """

  class BindingsValueListEntry(_messages.Message):
    r"""A BindingsValueListEntry object.

    Fields:
      condition: The condition that is associated with this binding. NOTE: an
        unsatisfied condition will not allow user access via current binding.
        Different bindings, including their conditions, are examined
        independently.
      members: A collection of identifiers for members who may assume the
        provided role. Recognized identifiers are as follows:   - allUsers - A
        special identifier that represents anyone on the internet; with or
        without a Google account.   - allAuthenticatedUsers - A special
        identifier that represents anyone who is authenticated with a Google
        account or a service account.   - user:emailid - An email address that
        represents a specific account. For example, user:<EMAIL> or
        user:<EMAIL>.   - serviceAccount:emailid - An email address
        that represents a service account. For example,  serviceAccount:my-
        <EMAIL> .   - group:emailid - An email
        address that represents a Google group. For example,
        group:<EMAIL>.   - domain:domain - A Google Apps domain
        name that represents all the users of that domain. For example,
        domain:google.com or domain:example.com.   - projectOwner:projectid -
        Owners of the given project. For example, projectOwner:my-example-
        project   - projectEditor:projectid - Editors of the given project.
        For example, projectEditor:my-example-project   -
        projectViewer:projectid - Viewers of the given project. For example,
        projectViewer:my-example-project
      role: The role to which members belong. Two types of roles are
        supported: new IAM roles, which grant permissions that do not map
        directly to those provided by ACLs, and legacy IAM roles, which do map
        directly to ACL permissions. All roles are of the format
        roles/storage.specificRole. The new IAM roles are:   -
        roles/storage.admin - Full control of Google Cloud Storage resources.
        - roles/storage.objectViewer - Read-Only access to Google Cloud
        Storage objects.   - roles/storage.objectCreator - Access to create
        objects in Google Cloud Storage.   - roles/storage.objectAdmin - Full
        control of Google Cloud Storage objects.   The legacy IAM roles are:
        - roles/storage.legacyObjectReader - Read-only access to objects
        without listing. Equivalent to an ACL entry on an object with the
        READER role.   - roles/storage.legacyObjectOwner - Read/write access
        to existing objects without listing. Equivalent to an ACL entry on an
        object with the OWNER role.   - roles/storage.legacyBucketReader -
        Read access to buckets with object listing. Equivalent to an ACL entry
        on a bucket with the READER role.   - roles/storage.legacyBucketWriter
        - Read access to buckets with object listing/creation/deletion.
        Equivalent to an ACL entry on a bucket with the WRITER role.   -
        roles/storage.legacyBucketOwner - Read and write access to existing
        buckets with object listing/creation/deletion. Equivalent to an ACL
        entry on a bucket with the OWNER role.
    """

    condition = _messages.MessageField('Expr', 1)
    members = _messages.StringField(2, repeated=True)
    role = _messages.StringField(3)

  bindings = _messages.MessageField('BindingsValueListEntry', 1, repeated=True)
  etag = _messages.BytesField(2)
  kind = _messages.StringField(3, default=u'storage#policy')
  resourceId = _messages.StringField(4)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class RewriteResponse(_messages.Message):
  r"""A rewrite response.

  Fields:
    done: true if the copy is finished; otherwise, false if the copy is in
      progress. This property is always present in the response.
    kind: The kind of item this is.
    objectSize: The total size of the object being copied in bytes. This
      property is always present in the response.
    resource: A resource containing the metadata for the copied-to object.
      This property is present in the response only when copying completes.
    rewriteToken: A token to use in subsequent requests to continue copying
      data. This token is present in the response only when there is more data
      to copy.
    totalBytesRewritten: The total bytes written so far, which can be used to
      provide a waiting user with a progress indicator. This property is
      always present in the response.
  """

  done = _messages.BooleanField(1)
  kind = _messages.StringField(2, default=u'storage#rewriteResponse')
  objectSize = _messages.IntegerField(3)
  resource = _messages.MessageField('Object', 4)
  rewriteToken = _messages.StringField(5)
  totalBytesRewritten = _messages.IntegerField(6)


class ServiceAccount(_messages.Message):
  r"""A subscription to receive Google PubSub notifications.

  Fields:
    email_address: The ID of the notification.
    kind: The kind of item this is. For notifications, this is always
      storage#notification.
  """

  email_address = _messages.StringField(1)
  kind = _messages.StringField(2, default=u'storage#serviceAccount')


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    AltValueValuesEnum: Data format for the response.

  Fields:
    alt: Data format for the response.
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: An opaque string that represents a user for quota purposes.
      Must not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    userIp: Deprecated. Please use quotaUser instead.
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for the response.

    Values:
      json: Responses with Content-Type of application/json
    """
    json = 0

  alt = _messages.EnumField('AltValueValuesEnum', 1, default=u'json')
  fields = _messages.StringField(2)
  key = _messages.StringField(3)
  oauth_token = _messages.StringField(4)
  prettyPrint = _messages.BooleanField(5, default=True)
  quotaUser = _messages.StringField(6)
  trace = _messages.StringField(7)
  userIp = _messages.StringField(8)


class StorageBucketAccessControlsDeleteRequest(_messages.Message):
  r"""A StorageBucketAccessControlsDeleteRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageBucketAccessControlsDeleteResponse(_messages.Message):
  r"""An empty StorageBucketAccessControlsDelete response."""


class StorageBucketAccessControlsGetRequest(_messages.Message):
  r"""A StorageBucketAccessControlsGetRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageBucketAccessControlsInsertRequest(_messages.Message):
  r"""A StorageBucketAccessControlsInsertRequest object.

  Fields:
    bucket: Name of a bucket.
    bucketAccessControl: A BucketAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  bucketAccessControl = _messages.MessageField('BucketAccessControl', 2)
  userProject = _messages.StringField(3)


class StorageBucketAccessControlsListRequest(_messages.Message):
  r"""A StorageBucketAccessControlsListRequest object.

  Fields:
    bucket: Name of a bucket.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  userProject = _messages.StringField(2)


class StorageBucketAccessControlsPatchRequest(_messages.Message):
  r"""A StorageBucketAccessControlsPatchRequest object.

  Fields:
    bucket: Name of a bucket.
    bucketAccessControl: A BucketAccessControl resource to be passed as the
      request body.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  bucketAccessControl = _messages.MessageField('BucketAccessControl', 2)
  entity = _messages.StringField(3, required=True)
  userProject = _messages.StringField(4)


class StorageBucketAccessControlsUpdateRequest(_messages.Message):
  r"""A StorageBucketAccessControlsUpdateRequest object.

  Fields:
    bucket: Name of a bucket.
    bucketAccessControl: A BucketAccessControl resource to be passed as the
      request body.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  bucketAccessControl = _messages.MessageField('BucketAccessControl', 2)
  entity = _messages.StringField(3, required=True)
  userProject = _messages.StringField(4)


class StorageBucketsDeleteRequest(_messages.Message):
  r"""A StorageBucketsDeleteRequest object.

  Fields:
    bucket: Name of a bucket.
    ifMetagenerationMatch: If set, only deletes the bucket if its
      metageneration matches this value.
    ifMetagenerationNotMatch: If set, only deletes the bucket if its
      metageneration does not match this value.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  ifMetagenerationMatch = _messages.IntegerField(2)
  ifMetagenerationNotMatch = _messages.IntegerField(3)
  userProject = _messages.StringField(4)


class StorageBucketsDeleteResponse(_messages.Message):
  r"""An empty StorageBucketsDelete response."""


class StorageBucketsGetIamPolicyRequest(_messages.Message):
  r"""A StorageBucketsGetIamPolicyRequest object.

  Fields:
    bucket: Name of a bucket.
    provisionalUserProject: The project to be billed for this request if the
      target bucket is requester-pays bucket.
    optionsRequestedPolicyVersion: The policy format version to be returned in
      the response.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  provisionalUserProject = _messages.StringField(2)
  optionsRequestedPolicyVersion = _messages.IntegerField(
      3, variant=_messages.Variant.INT32)
  userProject = _messages.StringField(4)


class StorageBucketsGetRequest(_messages.Message):
  r"""A StorageBucketsGetRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl.

  Fields:
    bucket: Name of a bucket.
    ifMetagenerationMatch: Makes the return of the bucket metadata conditional
      on whether the bucket's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the return of the bucket metadata
      conditional on whether the bucket's current metageneration does not
      match the given value.
    projection: Set of properties to return. Defaults to noAcl.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl.

    Values:
      full: Include all properties.
      noAcl: Omit owner, acl and defaultObjectAcl properties.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  ifMetagenerationMatch = _messages.IntegerField(2)
  ifMetagenerationNotMatch = _messages.IntegerField(3)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 4)
  userProject = _messages.StringField(5)


class StorageBucketsInsertRequest(_messages.Message):
  r"""A StorageBucketsInsertRequest object.

  Enums:
    PredefinedAclValueValuesEnum: Apply a predefined set of access controls to
      this bucket.
    PredefinedDefaultObjectAclValueValuesEnum: Apply a predefined set of
      default object access controls to this bucket.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl,
      unless the bucket resource specifies acl or defaultObjectAcl properties,
      when it defaults to full.

  Fields:
    bucket: A Bucket resource to be passed as the request body.
    predefinedAcl: Apply a predefined set of access controls to this bucket.
    predefinedDefaultObjectAcl: Apply a predefined set of default object
      access controls to this bucket.
    project: A valid API project identifier.
    projection: Set of properties to return. Defaults to noAcl, unless the
      bucket resource specifies acl or defaultObjectAcl properties, when it
      defaults to full.
    userProject: The project to be billed for this request.
  """

  class PredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to this bucket.

    Values:
      authenticatedRead: Project team owners get OWNER access, and
        allAuthenticatedUsers get READER access.
      private: Project team owners get OWNER access.
      projectPrivate: Project team members get access according to their
        roles.
      publicRead: Project team owners get OWNER access, and allUsers get
        READER access.
      publicReadWrite: Project team owners get OWNER access, and allUsers get
        WRITER access.
    """
    authenticatedRead = 0
    private = 1
    projectPrivate = 2
    publicRead = 3
    publicReadWrite = 4

  class PredefinedDefaultObjectAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of default object access controls to this
    bucket.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl, unless the bucket
    resource specifies acl or defaultObjectAcl properties, when it defaults to
    full.

    Values:
      full: Include all properties.
      noAcl: Omit owner, acl and defaultObjectAcl properties.
    """
    full = 0
    noAcl = 1

  bucket = _messages.MessageField('Bucket', 1)
  predefinedAcl = _messages.EnumField('PredefinedAclValueValuesEnum', 2)
  predefinedDefaultObjectAcl = _messages.EnumField(
      'PredefinedDefaultObjectAclValueValuesEnum', 3)
  project = _messages.StringField(4, required=True)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 5)
  userProject = _messages.StringField(6)


class StorageBucketsListRequest(_messages.Message):
  r"""A StorageBucketsListRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl.

  Fields:
    maxResults: Maximum number of buckets to return in a single response. The
      service will use this parameter or 1,000 items, whichever is smaller.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
    prefix: Filter results to buckets whose names begin with this prefix.
    project: A valid API project identifier.
    projection: Set of properties to return. Defaults to noAcl.
    userProject: The project to be billed for this request.
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl.

    Values:
      full: Include all properties.
      noAcl: Omit owner, acl and defaultObjectAcl properties.
    """
    full = 0
    noAcl = 1

  maxResults = _messages.IntegerField(1,
                                      variant=_messages.Variant.UINT32,
                                      default=1000)
  pageToken = _messages.StringField(2)
  prefix = _messages.StringField(3)
  project = _messages.StringField(4, required=True)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 5)
  userProject = _messages.StringField(6)


class StorageBucketsListChannelsRequest(_messages.Message):
  r"""A StorageBucketsListChannelsRequest object.

  Fields:
    bucket: Name of a bucket.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  userProject = _messages.StringField(2)


class StorageBucketsLockRetentionPolicyRequest(_messages.Message):
  r"""A StorageBucketsLockRetentionPolicyRequest object.

  Fields:
    bucket: Name of a bucket.
    ifMetagenerationMatch: Makes the operation conditional on whether bucket's
      current metageneration matches the given value.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  ifMetagenerationMatch = _messages.IntegerField(2, required=True)
  userProject = _messages.StringField(3)


class StorageBucketsPatchRequest(_messages.Message):
  r"""A StorageBucketsPatchRequest object.

  Enums:
    PredefinedAclValueValuesEnum: Apply a predefined set of access controls to
      this bucket.
    PredefinedDefaultObjectAclValueValuesEnum: Apply a predefined set of
      default object access controls to this bucket.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to full.

  Fields:
    bucket: Name of a bucket.
    bucketResource: A Bucket resource to be passed as the request body.
    ifMetagenerationMatch: Makes the return of the bucket metadata conditional
      on whether the bucket's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the return of the bucket metadata
      conditional on whether the bucket's current metageneration does not
      match the given value.
    predefinedAcl: Apply a predefined set of access controls to this bucket.
    predefinedDefaultObjectAcl: Apply a predefined set of default object
      access controls to this bucket.
    projection: Set of properties to return. Defaults to full.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class PredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to this bucket.

    Values:
      authenticatedRead: Project team owners get OWNER access, and
        allAuthenticatedUsers get READER access.
      private: Project team owners get OWNER access.
      projectPrivate: Project team members get access according to their
        roles.
      publicRead: Project team owners get OWNER access, and allUsers get
        READER access.
      publicReadWrite: Project team owners get OWNER access, and allUsers get
        WRITER access.
    """
    authenticatedRead = 0
    private = 1
    projectPrivate = 2
    publicRead = 3
    publicReadWrite = 4

  class PredefinedDefaultObjectAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of default object access controls to this
    bucket.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit owner, acl and defaultObjectAcl properties.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  bucketResource = _messages.MessageField('Bucket', 2)
  ifMetagenerationMatch = _messages.IntegerField(3)
  ifMetagenerationNotMatch = _messages.IntegerField(4)
  predefinedAcl = _messages.EnumField('PredefinedAclValueValuesEnum', 5)
  predefinedDefaultObjectAcl = _messages.EnumField(
      'PredefinedDefaultObjectAclValueValuesEnum', 6)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 7)
  userProject = _messages.StringField(8)


class StorageBucketsSetIamPolicyRequest(_messages.Message):
  r"""A StorageBucketsSetIamPolicyRequest object.

  Fields:
    bucket: Name of a bucket.
    policy: A Policy resource to be passed as the request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  policy = _messages.MessageField('Policy', 2)
  userProject = _messages.StringField(3)


class StorageBucketsTestIamPermissionsRequest(_messages.Message):
  r"""A StorageBucketsTestIamPermissionsRequest object.

  Fields:
    bucket: Name of a bucket.
    permissions: Permissions to test.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  permissions = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageBucketsUpdateRequest(_messages.Message):
  r"""A StorageBucketsUpdateRequest object.

  Enums:
    PredefinedAclValueValuesEnum: Apply a predefined set of access controls to
      this bucket.
    PredefinedDefaultObjectAclValueValuesEnum: Apply a predefined set of
      default object access controls to this bucket.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to full.

  Fields:
    bucket: Name of a bucket.
    bucketResource: A Bucket resource to be passed as the request body.
    ifMetagenerationMatch: Makes the return of the bucket metadata conditional
      on whether the bucket's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the return of the bucket metadata
      conditional on whether the bucket's current metageneration does not
      match the given value.
    predefinedAcl: Apply a predefined set of access controls to this bucket.
    predefinedDefaultObjectAcl: Apply a predefined set of default object
      access controls to this bucket.
    projection: Set of properties to return. Defaults to full.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class PredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to this bucket.

    Values:
      authenticatedRead: Project team owners get OWNER access, and
        allAuthenticatedUsers get READER access.
      private: Project team owners get OWNER access.
      projectPrivate: Project team members get access according to their
        roles.
      publicRead: Project team owners get OWNER access, and allUsers get
        READER access.
      publicReadWrite: Project team owners get OWNER access, and allUsers get
        WRITER access.
    """
    authenticatedRead = 0
    private = 1
    projectPrivate = 2
    publicRead = 3
    publicReadWrite = 4

  class PredefinedDefaultObjectAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of default object access controls to this
    bucket.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit owner, acl and defaultObjectAcl properties.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  bucketResource = _messages.MessageField('Bucket', 2)
  ifMetagenerationMatch = _messages.IntegerField(3)
  ifMetagenerationNotMatch = _messages.IntegerField(4)
  predefinedAcl = _messages.EnumField('PredefinedAclValueValuesEnum', 5)
  predefinedDefaultObjectAcl = _messages.EnumField(
      'PredefinedDefaultObjectAclValueValuesEnum', 6)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 7)
  userProject = _messages.StringField(8)


class StorageChannelsStopResponse(_messages.Message):
  r"""An empty StorageChannelsStop response."""


class StorageDefaultObjectAccessControlsDeleteRequest(_messages.Message):
  r"""A StorageDefaultObjectAccessControlsDeleteRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageDefaultObjectAccessControlsDeleteResponse(_messages.Message):
  r"""An empty StorageDefaultObjectAccessControlsDelete response."""


class StorageDefaultObjectAccessControlsGetRequest(_messages.Message):
  r"""A StorageDefaultObjectAccessControlsGetRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageDefaultObjectAccessControlsInsertRequest(_messages.Message):
  r"""A StorageDefaultObjectAccessControlsInsertRequest object.

  Fields:
    bucket: Name of a bucket.
    objectAccessControl: A ObjectAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  objectAccessControl = _messages.MessageField('ObjectAccessControl', 2)
  userProject = _messages.StringField(3)


class StorageDefaultObjectAccessControlsListRequest(_messages.Message):
  r"""A StorageDefaultObjectAccessControlsListRequest object.

  Fields:
    bucket: Name of a bucket.
    ifMetagenerationMatch: If present, only return default ACL listing if the
      bucket's current metageneration matches this value.
    ifMetagenerationNotMatch: If present, only return default ACL listing if
      the bucket's current metageneration does not match the given value.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  ifMetagenerationMatch = _messages.IntegerField(2)
  ifMetagenerationNotMatch = _messages.IntegerField(3)
  userProject = _messages.StringField(4)


class StorageDefaultObjectAccessControlsPatchRequest(_messages.Message):
  r"""A StorageDefaultObjectAccessControlsPatchRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    objectAccessControl: A ObjectAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  objectAccessControl = _messages.MessageField('ObjectAccessControl', 3)
  userProject = _messages.StringField(4)


class StorageDefaultObjectAccessControlsUpdateRequest(_messages.Message):
  r"""A StorageDefaultObjectAccessControlsUpdateRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    objectAccessControl: A ObjectAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  objectAccessControl = _messages.MessageField('ObjectAccessControl', 3)
  userProject = _messages.StringField(4)


class StorageNotificationsDeleteRequest(_messages.Message):
  r"""A StorageNotificationsDeleteRequest object.

  Fields:
    bucket: The parent bucket of the notification.
    notification: ID of the notification to delete.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  notification = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageNotificationsDeleteResponse(_messages.Message):
  r"""An empty StorageNotificationsDelete response."""


class StorageNotificationsGetRequest(_messages.Message):
  r"""A StorageNotificationsGetRequest object.

  Fields:
    bucket: The parent bucket of the notification.
    notification: Notification ID
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  notification = _messages.StringField(2, required=True)
  userProject = _messages.StringField(3)


class StorageNotificationsInsertRequest(_messages.Message):
  r"""A StorageNotificationsInsertRequest object.

  Fields:
    bucket: The parent bucket of the notification.
    notification: A Notification resource to be passed as the request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  notification = _messages.MessageField('Notification', 2)
  userProject = _messages.StringField(3)


class StorageNotificationsListRequest(_messages.Message):
  r"""A StorageNotificationsListRequest object.

  Fields:
    bucket: Name of a Google Cloud Storage bucket.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  userProject = _messages.StringField(2)


class StorageObjectAccessControlsDeleteRequest(_messages.Message):
  r"""A StorageObjectAccessControlsDeleteRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  generation = _messages.IntegerField(3)
  object = _messages.StringField(4, required=True)
  userProject = _messages.StringField(5)


class StorageObjectAccessControlsDeleteResponse(_messages.Message):
  r"""An empty StorageObjectAccessControlsDelete response."""


class StorageObjectAccessControlsGetRequest(_messages.Message):
  r"""A StorageObjectAccessControlsGetRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  generation = _messages.IntegerField(3)
  object = _messages.StringField(4, required=True)
  userProject = _messages.StringField(5)


class StorageObjectAccessControlsInsertRequest(_messages.Message):
  r"""A StorageObjectAccessControlsInsertRequest object.

  Fields:
    bucket: Name of a bucket.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    objectAccessControl: A ObjectAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3, required=True)
  objectAccessControl = _messages.MessageField('ObjectAccessControl', 4)
  userProject = _messages.StringField(5)


class StorageObjectAccessControlsListRequest(_messages.Message):
  r"""A StorageObjectAccessControlsListRequest object.

  Fields:
    bucket: Name of a bucket.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3, required=True)
  userProject = _messages.StringField(4)


class StorageObjectAccessControlsPatchRequest(_messages.Message):
  r"""A StorageObjectAccessControlsPatchRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    objectAccessControl: A ObjectAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  generation = _messages.IntegerField(3)
  object = _messages.StringField(4, required=True)
  objectAccessControl = _messages.MessageField('ObjectAccessControl', 5)
  userProject = _messages.StringField(6)


class StorageObjectAccessControlsUpdateRequest(_messages.Message):
  r"""A StorageObjectAccessControlsUpdateRequest object.

  Fields:
    bucket: Name of a bucket.
    entity: The entity holding the permission. Can be user-userId, user-
      emailAddress, group-groupId, group-emailAddress, allUsers, or
      allAuthenticatedUsers.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    objectAccessControl: A ObjectAccessControl resource to be passed as the
      request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  entity = _messages.StringField(2, required=True)
  generation = _messages.IntegerField(3)
  object = _messages.StringField(4, required=True)
  objectAccessControl = _messages.MessageField('ObjectAccessControl', 5)
  userProject = _messages.StringField(6)


class StorageObjectsComposeRequest(_messages.Message):
  r"""A StorageObjectsComposeRequest object.

  Enums:
    DestinationPredefinedAclValueValuesEnum: Apply a predefined set of access
      controls to the destination object.

  Fields:
    composeRequest: A ComposeRequest resource to be passed as the request
      body.
    destinationBucket: Name of the bucket containing the source objects. The
      destination object is stored in this bucket.
    destinationObject: Name of the new object. For information about how to
      URL encode object names to be path safe, see Encoding URI Path Parts.
    destinationPredefinedAcl: Apply a predefined set of access controls to the
      destination object.
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's current metageneration matches the given value.
    kmsKeyName: Resource name of the Cloud KMS key, of the form projects/my-
      project/locations/global/keyRings/my-kr/cryptoKeys/my-key, that will be
      used to encrypt the object. Overrides the object metadata's kms_key_name
      value, if any.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class DestinationPredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to the destination object.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  composeRequest = _messages.MessageField('ComposeRequest', 1)
  destinationBucket = _messages.StringField(2, required=True)
  destinationObject = _messages.StringField(3, required=True)
  destinationPredefinedAcl = _messages.EnumField(
      'DestinationPredefinedAclValueValuesEnum', 4)
  ifGenerationMatch = _messages.IntegerField(5)
  ifMetagenerationMatch = _messages.IntegerField(6)
  kmsKeyName = _messages.StringField(7)
  userProject = _messages.StringField(8)


class StorageObjectsCopyRequest(_messages.Message):
  r"""A StorageObjectsCopyRequest object.

  Enums:
    DestinationPredefinedAclValueValuesEnum: Apply a predefined set of access
      controls to the destination object.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl,
      unless the object resource specifies the acl property, when it defaults
      to full.

  Fields:
    destinationBucket: Name of the bucket in which to store the new object.
      Overrides the provided object metadata's bucket value, if any.For
      information about how to URL encode object names to be path safe, see
      Encoding URI Path Parts.
    destinationObject: Name of the new object. Required when the object
      metadata is not otherwise provided. Overrides the object metadata's name
      value, if any.
    destinationPredefinedAcl: Apply a predefined set of access controls to the
      destination object.
    ifGenerationMatch: Makes the operation conditional on whether the
      destination object's current generation matches the given value. Setting
      to 0 makes the operation succeed only if there are no live versions of
      the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      destination object's current generation does not match the given value.
      If no live object exists, the precondition fails. Setting to 0 makes the
      operation succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      destination object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      destination object's current metageneration does not match the given
      value.
    ifSourceGenerationMatch: Makes the operation conditional on whether the
      source object's current generation matches the given value.
    ifSourceGenerationNotMatch: Makes the operation conditional on whether the
      source object's current generation does not match the given value.
    ifSourceMetagenerationMatch: Makes the operation conditional on whether
      the source object's current metageneration matches the given value.
    ifSourceMetagenerationNotMatch: Makes the operation conditional on whether
      the source object's current metageneration does not match the given
      value.
    object: A Object resource to be passed as the request body.
    projection: Set of properties to return. Defaults to noAcl, unless the
      object resource specifies the acl property, when it defaults to full.
    sourceBucket: Name of the bucket in which to find the source object.
    sourceGeneration: If present, selects a specific revision of the source
      object (as opposed to the latest version, the default).
    sourceObject: Name of the source object. For information about how to URL
      encode object names to be path safe, see Encoding URI Path Parts.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class DestinationPredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to the destination object.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl, unless the object
    resource specifies the acl property, when it defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  destinationBucket = _messages.StringField(1, required=True)
  destinationObject = _messages.StringField(2, required=True)
  destinationPredefinedAcl = _messages.EnumField(
      'DestinationPredefinedAclValueValuesEnum', 3)
  ifGenerationMatch = _messages.IntegerField(4)
  ifGenerationNotMatch = _messages.IntegerField(5)
  ifMetagenerationMatch = _messages.IntegerField(6)
  ifMetagenerationNotMatch = _messages.IntegerField(7)
  ifSourceGenerationMatch = _messages.IntegerField(8)
  ifSourceGenerationNotMatch = _messages.IntegerField(9)
  ifSourceMetagenerationMatch = _messages.IntegerField(10)
  ifSourceMetagenerationNotMatch = _messages.IntegerField(11)
  object = _messages.MessageField('Object', 12)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 13)
  sourceBucket = _messages.StringField(14, required=True)
  sourceGeneration = _messages.IntegerField(15)
  sourceObject = _messages.StringField(16, required=True)
  userProject = _messages.StringField(17)


class StorageObjectsDeleteRequest(_messages.Message):
  r"""A StorageObjectsDeleteRequest object.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, permanently deletes a specific revision of this
      object (as opposed to the latest version, the default).
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      object's current generation does not match the given value. If no live
      object exists, the precondition fails. Setting to 0 makes the operation
      succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      object's current metageneration does not match the given value.
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  ifGenerationMatch = _messages.IntegerField(3)
  ifGenerationNotMatch = _messages.IntegerField(4)
  ifMetagenerationMatch = _messages.IntegerField(5)
  ifMetagenerationNotMatch = _messages.IntegerField(6)
  object = _messages.StringField(7, required=True)
  userProject = _messages.StringField(8)


class StorageObjectsDeleteResponse(_messages.Message):
  r"""An empty StorageObjectsDelete response."""


class StorageObjectsGetIamPolicyRequest(_messages.Message):
  r"""A StorageObjectsGetIamPolicyRequest object.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3, required=True)
  userProject = _messages.StringField(4)


class StorageObjectsGetRequest(_messages.Message):
  r"""A StorageObjectsGetRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      object's current generation does not match the given value. If no live
      object exists, the precondition fails. Setting to 0 makes the operation
      succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      object's current metageneration does not match the given value.
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    projection: Set of properties to return. Defaults to noAcl.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  ifGenerationMatch = _messages.IntegerField(3)
  ifGenerationNotMatch = _messages.IntegerField(4)
  ifMetagenerationMatch = _messages.IntegerField(5)
  ifMetagenerationNotMatch = _messages.IntegerField(6)
  object = _messages.StringField(7, required=True)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 8)
  userProject = _messages.StringField(9)


class StorageObjectsInsertRequest(_messages.Message):
  r"""A StorageObjectsInsertRequest object.

  Enums:
    PredefinedAclValueValuesEnum: Apply a predefined set of access controls to
      this object.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl,
      unless the object resource specifies the acl property, when it defaults
      to full.

  Fields:
    bucket: Name of the bucket in which to store the new object. Overrides the
      provided object metadata's bucket value, if any.
    contentEncoding: If set, sets the contentEncoding property of the final
      object to this value. Setting this parameter is equivalent to setting
      the contentEncoding metadata property. This can be useful when uploading
      an object with uploadType=media to indicate the encoding of the content
      being uploaded.
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      object's current generation does not match the given value. If no live
      object exists, the precondition fails. Setting to 0 makes the operation
      succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      object's current metageneration does not match the given value.
    kmsKeyName: Resource name of the Cloud KMS key, of the form projects/my-
      project/locations/global/keyRings/my-kr/cryptoKeys/my-key, that will be
      used to encrypt the object. Overrides the object metadata's kms_key_name
      value, if any.
    name: Name of the object. Required when the object metadata is not
      otherwise provided. Overrides the object metadata's name value, if any.
      For information about how to URL encode object names to be path safe,
      see Encoding URI Path Parts.
    object: A Object resource to be passed as the request body.
    predefinedAcl: Apply a predefined set of access controls to this object.
    projection: Set of properties to return. Defaults to noAcl, unless the
      object resource specifies the acl property, when it defaults to full.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class PredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to this object.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl, unless the object
    resource specifies the acl property, when it defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  contentEncoding = _messages.StringField(2)
  ifGenerationMatch = _messages.IntegerField(3)
  ifGenerationNotMatch = _messages.IntegerField(4)
  ifMetagenerationMatch = _messages.IntegerField(5)
  ifMetagenerationNotMatch = _messages.IntegerField(6)
  kmsKeyName = _messages.StringField(7)
  name = _messages.StringField(8)
  object = _messages.MessageField('Object', 9)
  predefinedAcl = _messages.EnumField('PredefinedAclValueValuesEnum', 10)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 11)
  userProject = _messages.StringField(12)


class StorageObjectsListRequest(_messages.Message):
  r"""A StorageObjectsListRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl.

  Fields:
    bucket: Name of the bucket in which to look for objects.
    delimiter: Returns results in a directory-like mode. items will contain
      only objects whose names, aside from the prefix, do not contain
      delimiter. Objects whose names, aside from the prefix, contain delimiter
      will have their name, truncated after the delimiter, returned in
      prefixes. Duplicate prefixes are omitted.
    includeTrailingDelimiter: If true, objects that end in exactly one
      instance of delimiter will have their metadata included in items in
      addition to prefixes.
    maxResults: Maximum number of items plus prefixes to return in a single
      page of responses. As duplicate prefixes are omitted, fewer total
      results may be returned than requested. The service will use this
      parameter or 1,000 items, whichever is smaller.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
    prefix: Filter results to objects whose names begin with this prefix.
    projection: Set of properties to return. Defaults to noAcl.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
    versions: If true, lists all versions of an object as distinct results.
      The default is false. For more information, see Object Versioning.
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  delimiter = _messages.StringField(2)
  includeTrailingDelimiter = _messages.BooleanField(3)
  maxResults = _messages.IntegerField(4,
                                      variant=_messages.Variant.UINT32,
                                      default=1000)
  pageToken = _messages.StringField(5)
  prefix = _messages.StringField(6)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 7)
  userProject = _messages.StringField(8)
  versions = _messages.BooleanField(9)


class StorageObjectsPatchRequest(_messages.Message):
  r"""A StorageObjectsPatchRequest object.

  Enums:
    PredefinedAclValueValuesEnum: Apply a predefined set of access controls to
      this object.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to full.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      object's current generation does not match the given value. If no live
      object exists, the precondition fails. Setting to 0 makes the operation
      succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      object's current metageneration does not match the given value.
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    objectResource: A Object resource to be passed as the request body.
    predefinedAcl: Apply a predefined set of access controls to this object.
    projection: Set of properties to return. Defaults to full.
    userProject: The project to be billed for this request, for Requester Pays
      buckets.
  """

  class PredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to this object.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  ifGenerationMatch = _messages.IntegerField(3)
  ifGenerationNotMatch = _messages.IntegerField(4)
  ifMetagenerationMatch = _messages.IntegerField(5)
  ifMetagenerationNotMatch = _messages.IntegerField(6)
  object = _messages.StringField(7, required=True)
  objectResource = _messages.MessageField('Object', 8)
  predefinedAcl = _messages.EnumField('PredefinedAclValueValuesEnum', 9)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 10)
  userProject = _messages.StringField(11)


class StorageObjectsRewriteRequest(_messages.Message):
  r"""A StorageObjectsRewriteRequest object.

  Enums:
    DestinationPredefinedAclValueValuesEnum: Apply a predefined set of access
      controls to the destination object.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl,
      unless the object resource specifies the acl property, when it defaults
      to full.

  Fields:
    destinationBucket: Name of the bucket in which to store the new object.
      Overrides the provided object metadata's bucket value, if any.
    destinationKmsKeyName: Resource name of the Cloud KMS key, of the form
      projects/my-project/locations/global/keyRings/my-kr/cryptoKeys/my-key,
      that will be used to encrypt the object. Overrides the object metadata's
      kms_key_name value, if any.
    destinationObject: Name of the new object. Required when the object
      metadata is not otherwise provided. Overrides the object metadata's name
      value, if any. For information about how to URL encode object names to
      be path safe, see Encoding URI Path Parts.
    destinationPredefinedAcl: Apply a predefined set of access controls to the
      destination object.
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      object's current generation does not match the given value. If no live
      object exists, the precondition fails. Setting to 0 makes the operation
      succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      destination object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      destination object's current metageneration does not match the given
      value.
    ifSourceGenerationMatch: Makes the operation conditional on whether the
      source object's current generation matches the given value.
    ifSourceGenerationNotMatch: Makes the operation conditional on whether the
      source object's current generation does not match the given value.
    ifSourceMetagenerationMatch: Makes the operation conditional on whether
      the source object's current metageneration matches the given value.
    ifSourceMetagenerationNotMatch: Makes the operation conditional on whether
      the source object's current metageneration does not match the given
      value.
    maxBytesRewrittenPerCall: The maximum number of bytes that will be
      rewritten per rewrite request. Most callers shouldn't need to specify
      this parameter - it is primarily in place to support testing. If
      specified the value must be an integral multiple of 1 MiB (1048576).
      Also, this only applies to requests where the source and destination
      span locations and/or storage classes. Finally, this value must not
      change across rewrite calls else you'll get an error that the
      rewriteToken is invalid.
    object: A Object resource to be passed as the request body.
    projection: Set of properties to return. Defaults to noAcl, unless the
      object resource specifies the acl property, when it defaults to full.
    rewriteToken: Include this field (from the previous rewrite response) on
      each rewrite request after the first one, until the rewrite response
      'done' flag is true. Calls that provide a rewriteToken can omit all
      other request fields, but if included those fields must match the values
      provided in the first rewrite request.
    sourceBucket: Name of the bucket in which to find the source object.
    sourceGeneration: If present, selects a specific revision of the source
      object (as opposed to the latest version, the default).
    sourceObject: Name of the source object. For information about how to URL
      encode object names to be path safe, see Encoding URI Path Parts.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class DestinationPredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to the destination object.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl, unless the object
    resource specifies the acl property, when it defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  destinationBucket = _messages.StringField(1, required=True)
  destinationKmsKeyName = _messages.StringField(2)
  destinationObject = _messages.StringField(3, required=True)
  destinationPredefinedAcl = _messages.EnumField(
      'DestinationPredefinedAclValueValuesEnum', 4)
  ifGenerationMatch = _messages.IntegerField(5)
  ifGenerationNotMatch = _messages.IntegerField(6)
  ifMetagenerationMatch = _messages.IntegerField(7)
  ifMetagenerationNotMatch = _messages.IntegerField(8)
  ifSourceGenerationMatch = _messages.IntegerField(9)
  ifSourceGenerationNotMatch = _messages.IntegerField(10)
  ifSourceMetagenerationMatch = _messages.IntegerField(11)
  ifSourceMetagenerationNotMatch = _messages.IntegerField(12)
  maxBytesRewrittenPerCall = _messages.IntegerField(13)
  object = _messages.MessageField('Object', 14)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 15)
  rewriteToken = _messages.StringField(16)
  sourceBucket = _messages.StringField(17, required=True)
  sourceGeneration = _messages.IntegerField(18)
  sourceObject = _messages.StringField(19, required=True)
  userProject = _messages.StringField(20)


class StorageObjectsSetIamPolicyRequest(_messages.Message):
  r"""A StorageObjectsSetIamPolicyRequest object.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    policy: A Policy resource to be passed as the request body.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3, required=True)
  policy = _messages.MessageField('Policy', 4)
  userProject = _messages.StringField(5)


class StorageObjectsTestIamPermissionsRequest(_messages.Message):
  r"""A StorageObjectsTestIamPermissionsRequest object.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    permissions: Permissions to test.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3, required=True)
  permissions = _messages.StringField(4, required=True)
  userProject = _messages.StringField(5)


class StorageObjectsUpdateRequest(_messages.Message):
  r"""A StorageObjectsUpdateRequest object.

  Enums:
    PredefinedAclValueValuesEnum: Apply a predefined set of access controls to
      this object.
    ProjectionValueValuesEnum: Set of properties to return. Defaults to full.

  Fields:
    bucket: Name of the bucket in which the object resides.
    generation: If present, selects a specific revision of this object (as
      opposed to the latest version, the default).
    ifGenerationMatch: Makes the operation conditional on whether the object's
      current generation matches the given value. Setting to 0 makes the
      operation succeed only if there are no live versions of the object.
    ifGenerationNotMatch: Makes the operation conditional on whether the
      object's current generation does not match the given value. If no live
      object exists, the precondition fails. Setting to 0 makes the operation
      succeed only if there is a live version of the object.
    ifMetagenerationMatch: Makes the operation conditional on whether the
      object's current metageneration matches the given value.
    ifMetagenerationNotMatch: Makes the operation conditional on whether the
      object's current metageneration does not match the given value.
    object: Name of the object. For information about how to URL encode object
      names to be path safe, see Encoding URI Path Parts.
    objectResource: A Object resource to be passed as the request body.
    predefinedAcl: Apply a predefined set of access controls to this object.
    projection: Set of properties to return. Defaults to full.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
  """

  class PredefinedAclValueValuesEnum(_messages.Enum):
    r"""Apply a predefined set of access controls to this object.

    Values:
      authenticatedRead: Object owner gets OWNER access, and
        allAuthenticatedUsers get READER access.
      bucketOwnerFullControl: Object owner gets OWNER access, and project team
        owners get OWNER access.
      bucketOwnerRead: Object owner gets OWNER access, and project team owners
        get READER access.
      private: Object owner gets OWNER access.
      projectPrivate: Object owner gets OWNER access, and project team members
        get access according to their roles.
      publicRead: Object owner gets OWNER access, and allUsers get READER
        access.
    """
    authenticatedRead = 0
    bucketOwnerFullControl = 1
    bucketOwnerRead = 2
    private = 3
    projectPrivate = 4
    publicRead = 5

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to full.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  generation = _messages.IntegerField(2)
  ifGenerationMatch = _messages.IntegerField(3)
  ifGenerationNotMatch = _messages.IntegerField(4)
  ifMetagenerationMatch = _messages.IntegerField(5)
  ifMetagenerationNotMatch = _messages.IntegerField(6)
  object = _messages.StringField(7, required=True)
  objectResource = _messages.MessageField('Object', 8)
  predefinedAcl = _messages.EnumField('PredefinedAclValueValuesEnum', 9)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 10)
  userProject = _messages.StringField(11)


class StorageObjectsWatchAllRequest(_messages.Message):
  r"""A StorageObjectsWatchAllRequest object.

  Enums:
    ProjectionValueValuesEnum: Set of properties to return. Defaults to noAcl.

  Fields:
    bucket: Name of the bucket in which to look for objects.
    channel: A Channel resource to be passed as the request body.
    delimiter: Returns results in a directory-like mode. items will contain
      only objects whose names, aside from the prefix, do not contain
      delimiter. Objects whose names, aside from the prefix, contain delimiter
      will have their name, truncated after the delimiter, returned in
      prefixes. Duplicate prefixes are omitted.
    includeTrailingDelimiter: If true, objects that end in exactly one
      instance of delimiter will have their metadata included in items in
      addition to prefixes.
    maxResults: Maximum number of items plus prefixes to return in a single
      page of responses. As duplicate prefixes are omitted, fewer total
      results may be returned than requested. The service will use this
      parameter or 1,000 items, whichever is smaller.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
    prefix: Filter results to objects whose names begin with this prefix.
    projection: Set of properties to return. Defaults to noAcl.
    userProject: The project to be billed for this request. Required for
      Requester Pays buckets.
    versions: If true, lists all versions of an object as distinct results.
      The default is false. For more information, see Object Versioning.
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Set of properties to return. Defaults to noAcl.

    Values:
      full: Include all properties.
      noAcl: Omit the owner, acl property.
    """
    full = 0
    noAcl = 1

  bucket = _messages.StringField(1, required=True)
  channel = _messages.MessageField('Channel', 2)
  delimiter = _messages.StringField(3)
  includeTrailingDelimiter = _messages.BooleanField(4)
  maxResults = _messages.IntegerField(5,
                                      variant=_messages.Variant.UINT32,
                                      default=1000)
  pageToken = _messages.StringField(6)
  prefix = _messages.StringField(7)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 8)
  userProject = _messages.StringField(9)
  versions = _messages.BooleanField(10)


class StorageProjectsServiceAccountGetRequest(_messages.Message):
  r"""A StorageProjectsServiceAccountGetRequest object.

  Fields:
    projectId: Project ID
    userProject: The project to be billed for this request.
  """

  projectId = _messages.StringField(1, required=True)
  userProject = _messages.StringField(2)


class TestIamPermissionsResponse(_messages.Message):
  r"""A storage.(buckets|objects).testIamPermissions response.

  Fields:
    kind: The kind of item this is.
    permissions: The permissions held by the caller. Permissions are always of
      the format storage.resource.capability, where resource is one of buckets
      or objects. The supported permissions are as follows:   -
      storage.buckets.delete - Delete bucket.   - storage.buckets.get - Read
      bucket metadata.   - storage.buckets.getIamPolicy - Read bucket IAM
      policy.   - storage.buckets.create - Create bucket.   -
      storage.buckets.list - List buckets.   - storage.buckets.setIamPolicy -
      Update bucket IAM policy.   - storage.buckets.update - Update bucket
      metadata.   - storage.objects.delete - Delete object.   -
      storage.objects.get - Read object data and metadata.   -
      storage.objects.getIamPolicy - Read object IAM policy.   -
      storage.objects.create - Create object.   - storage.objects.list - List
      objects.   - storage.objects.setIamPolicy - Update object IAM policy.
      - storage.objects.update - Update object metadata.
  """

  kind = _messages.StringField(1, default=u'storage#testIamPermissionsResponse')
  permissions = _messages.StringField(2, repeated=True)


class StorageProjectsHmacKeysCreateRequest(_messages.Message):
  """A StorageProjectsHmacKeysCreateRequest object.

  Fields:
    projectId: Project ID
    serviceAccountEmail: Email address of the service account for which to
      create a key.
  """

  projectId = _messages.StringField(1, required=True)
  serviceAccountEmail = _messages.StringField(2, required=True)


class StorageProjectsHmacKeysDeleteRequest(_messages.Message):
  """A StorageProjectsHmacKeysDeleteRequest object.

  Fields:
    projectId: Project ID
    accessId: Name of the HMAC key to be deleted.
  """

  projectId = _messages.StringField(1, required=True)
  accessId = _messages.StringField(2, required=True)


class StorageProjectsHmacKeysGetRequest(_messages.Message):
  """A StorageProjectsHmacKeysGetRequest object.

  Fields:
    projectId: Project ID
    accessId: Name of the HMAC key for which the metadata is being requested.
  """

  projectId = _messages.StringField(2, required=True)
  accessId = _messages.StringField(1, required=True)


class StorageProjectsHmacKeysListRequest(_messages.Message):
  """A StorageProjectsHmacKeysListRequest object.

  Fields:
    projectId: Project ID
    serviceAccountEmail: If present only, keys for the given service account
      will be returned.
    showDeletedKeys: Whether or not to show keys in the DELETED state.
    maxResults: Maximum number of items to return in a single
      page of responses. The service will use this
      parameter or 1,000 items, whichever is smaller.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.

  """

  projectId = _messages.StringField(1, required=True)
  serviceAccountEmail = _messages.StringField(2)
  showDeletedKeys = _messages.BooleanField(3)
  maxResults = _messages.IntegerField(4,
                                      variant=_messages.Variant.UINT32,
                                      default=1000)
  pageToken = _messages.StringField(5)


class StorageProjectsHmacKeysUpdateRequest(_messages.Message):
  """A StorageProjectsHmacKeysUpdateRequest object.

  Fields:
    projectId: Project ID
    accessId: Name of the HMAC key being updated.
  """

  projectId = _messages.StringField(1, required=True)
  accessId = _messages.StringField(2, required=True)
  resource = _messages.MessageField('HmacKeyMetadata', 3)


class HmacKeyMetadata(_messages.Message):
  """An HMAC Key Metadata resource.

  Fields:
    id: The ID of the HMAC key, including the Project ID and the Access ID.
    selfLink: The link to this resource.
    accessId: The ID of the HMAC Key.
    projectId: Project ID owning the service account to which the key
       authenticates.
    serviceAccountEmail: The email address of the key's associated service
      account.
    state: The state of the key. Can be one of ACTIVE, INACTIVE, or DELETED.
    timeCreated: The creation time of the key in RFC 3339 format.
    updated: The modification time of the key in RFC 3339 format.
    etag: HTTP 1.1 Entity tag for the key.
    kind: The kind of item this is. For HMAC Key metadata, this is always
      storage#hmacKeyMetadata
  """

  id = _messages.StringField(1)
  selfLink = _messages.StringField(2)

  accessId = _messages.StringField(3)
  projectId = _messages.StringField(4)
  serviceAccountEmail = _messages.StringField(5)
  state = _messages.StringField(6)
  timeCreated = _message_types.DateTimeField(7)
  updated = _message_types.DateTimeField(8)
  etag = _messages.StringField(9)

  kind = _messages.StringField(10, default=u'storage#hmacKeyMetadata')


class HmacKey(_messages.Message):
  """An HMAC Key resource.

  Fields:
    metadata: Key metadata.
    secret: HMAC secret key material.
    kind: The kind of item this is. For HMAC keys, this is always
      storage#hmacKey.
  """

  metadata = _messages.MessageField('HmacKeyMetadata', 1)
  secret = _messages.StringField(2)
  kind = _messages.StringField(3, default=u'storage#hmacKey')


class HmacKeyMetadataList(_messages.Message):
  """A list of HMAC key metadata.

  Fields:
    items: The list of HMAC key metadata.
    kind: The kind of item this is. For a list of HMAC key, this is always
      storage#hmacKeysMetadata.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
  """

  items = _messages.MessageField('HmacKeyMetadata', 1, repeated=True)
  kind = _messages.StringField(2, default=u'storage#hmacKeysMetadata')
  nextPageToken = _messages.StringField(3)


class HmacKeysDeleteResponse(_messages.Message):
  """An empty HmacKeysDelete response."""
